import sys
import threading
import requests
from bs4 import BeautifulSoup
import re
import os
import html
from collections import deque
import time
import random
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, as_completed
from threading import Lock
import json
from datetime import datetime
import hashlib
from PySide6.QtCore import QThread, Signal
from typing import List, Dict
from urllib.parse import urljoin
from PySide6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout,
    QLineEdit, QPushButton, QTreeWidget, QTreeWidgetItem,
    QMessageBox, QLabel, QSizePolicy, QDialog,
    QScrollArea, QGridLayout, QCheckBox, QComboBox,
    QSpinBox, QFormLayout
)
from PySide6.QtWidgets import QProgressBar
from PySide6.QtGui import QPixmap
from PySide6.QtCore import Qt, QThread, QUrl, QSize, QSettings
from PySide6.QtNetwork import QNetworkAccessManager, QNetworkRequest
from PySide6.QtCore import Qt, QByteArray
import logging
from urllib.parse import urlparse, urlunparse
  
# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
  
# 常量
BASE_URL = "https://www.mxd009.cc"
  
def submit_search(keywords: str) -> str:
    """
    提交搜索请求，返回重定向后的 URL
    """
    form_data = {
        "keyboard": keywords,
        "show": "title",
        "tempid": "1",
        "tbname": "news"
    }
  
    SEARCH_URL = f"{BASE_URL}/e/search/index.php"
    session = requests.Session()
    response = session.post(SEARCH_URL, data=form_data, allow_redirects=False)
  
    if response.status_code == 302:
        new_location = response.headers.get("Location")
        return urljoin(SEARCH_URL, new_location)
    else:
        print("未发生重定向，状态码:", response.status_code)
        return ""
  
def get_total_count(soup: BeautifulSoup) -> int:
    """
    从页面中提取总图片组数
    """
    biaoqian_div = soup.find("div", class_="biaoqian")
    if biaoqian_div:
        p_text = biaoqian_div.find("p").get_text(strip=True)
        match = re.search(r"(\d+)", p_text)
        if match:
            return int(match.group(1))
    return 0
  
class DownloadProgressManager:
    """管理批量下载进度的持久化"""

    def __init__(self, progress_file="download_progress.json"):
        self.progress_file = progress_file
        self.current_session = None

    def create_session(self, session_name, total_items):
        """创建新的下载会话"""
        session_id = hashlib.md5(f"{session_name}_{datetime.now()}".encode()).hexdigest()[:8]
        self.current_session = {
            "session_id": session_id,
            "session_name": session_name,
            "created_time": datetime.now().isoformat(),
            "total_items": total_items,
            "completed_items": [],
            "failed_items": [],
            "current_item": None,
            "pending_items": [],
            "status": "active"  # active, paused, completed, cancelled
        }
        return session_id

    def add_pending_items(self, items):
        """添加待下载项目"""
        if self.current_session:
            self.current_session["pending_items"] = items
            self.save_progress()

    def mark_item_completed(self, item_data):
        """标记项目为已完成"""
        if self.current_session:
            # 添加完成时间戳
            item_data["completed_time"] = datetime.now().isoformat()
            self.current_session["completed_items"].append(item_data)

            # 从待下载列表中移除
            self.current_session["pending_items"] = [
                item for item in self.current_session["pending_items"]
                if item.get("url") != item_data.get("url")
            ]
            self.save_progress()

    def mark_item_failed(self, item_data, error_msg=""):
        """标记项目为失败"""
        if self.current_session:
            item_data["failed_time"] = datetime.now().isoformat()
            item_data["error_message"] = error_msg
            self.current_session["failed_items"].append(item_data)
            self.save_progress()

    def set_current_item(self, item_data):
        """设置当前正在下载的项目"""
        if self.current_session:
            self.current_session["current_item"] = item_data
            self.save_progress()

    def update_session_status(self, status):
        """更新会话状态"""
        if self.current_session:
            self.current_session["status"] = status
            self.current_session["last_update"] = datetime.now().isoformat()
            self.save_progress()

    def save_progress(self):
        """保存进度到文件"""
        try:
            # 读取现有数据
            existing_data = self.load_all_sessions()

            # 更新当前会话
            if self.current_session:
                existing_data[self.current_session["session_id"]] = self.current_session

            # 保存到文件
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存进度失败: {e}")

    def load_all_sessions(self):
        """加载所有会话数据"""
        try:
            if os.path.exists(self.progress_file):
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"加载进度失败: {e}")
            return {}

    def get_incomplete_sessions(self):
        """获取未完成的会话"""
        all_sessions = self.load_all_sessions()
        incomplete = {}
        for session_id, session_data in all_sessions.items():
            if session_data.get("status") in ["active", "paused"] and session_data.get("pending_items"):
                incomplete[session_id] = session_data
        return incomplete

    def load_session(self, session_id):
        """加载指定会话"""
        all_sessions = self.load_all_sessions()
        if session_id in all_sessions:
            self.current_session = all_sessions[session_id]
            return True
        return False

    def get_session_stats(self):
        """获取当前会话统计信息"""
        if not self.current_session:
            return None

        total = self.current_session["total_items"]
        completed = len(self.current_session["completed_items"])
        failed = len(self.current_session["failed_items"])
        pending = len(self.current_session["pending_items"])

        return {
            "total": total,
            "completed": completed,
            "failed": failed,
            "pending": pending,
            "progress_percent": int((completed / total * 100)) if total > 0 else 0
        }


def parse_gallery_items_from_root(soup: BeautifulSoup) -> List[Dict[str, str]]:
    """
    提取页面中所有图片组信息
    """
    gallery_root = soup.find("div", class_="box galleryList")
    items = []
  
    if not gallery_root:
        return items
  
    for li in gallery_root.select("ul.databox > li"):
        img_tag = li.select_one("div.img-box img")
        ztitle_tag = li.select_one("p.ztitle a")
        rtitle_tag = li.select_one("p.rtitle a")
        author_tag = li.select_one("p.ztitle font")
        # 1. 找到所有 class 为 img-box 的 div
        count_tag = li.select_one("em.num")
  
        href = ztitle_tag["href"] if ztitle_tag and ztitle_tag.has_attr("href") else ""
        full_link = urljoin(BASE_URL, href)
        count = 0
        if count_tag:
            text = count_tag.get_text(strip=True)  # '15P'
            match = re.search(r'\d+', text)
            if match:
                count = int(match.group(0))
                # print(f"图集图片数量: {count}")
         
        rtitle = rtitle_tag.get_text(strip=True) if rtitle_tag else ""
        if author_tag:
            author = author_tag.get_text(strip=True)
        else:
            author = rtitle
        item = {
            "img": img_tag["src"] if img_tag else "",
            "ztitle": ztitle_tag.get_text(strip=True) if ztitle_tag else "",
            "ztitle_href": full_link,
            "author": author,
            "rtitle": rtitle,
            "count":str(count)
        }
  
        items.append(item)
  
    return items
     
     
def crawl_single_gallery(url) -> List[Dict[str, str]]:
    """
    提取当前单页面信息
    """
     
    items = []
    try:
        response = requests.get(url, timeout=10)
        soup = BeautifulSoup(response.text, "html.parser")
         
        # 判断是否存在 id="tishi" 的 div
        tishi_div = soup.find('div', id='tishi')
 
        if tishi_div:
            # 未登陆情况下
            # 提取提示中说的“全本XX张图片”
            total_text = tishi_div.find('p').get_text() if tishi_div else ''
            match = re.search(r'全本(\d+)张图片', total_text)
            if match:
                total_count = int(match.group(1))
                #print(f"全图集共有 {total_count} 张图片") 
        else:
            # 登陆情况下，默认本程序是不登陆的
            # 从 id="page" 开始找
            page_div = soup.find('div', id='page')
 
            # 在 page_div 下找 span 含 "1/97" 结构
            if page_div:
                span = page_div.find('span', string=re.compile(r'\d+/\d+'))
                if span:
                    match = re.search(r'\d+/(\d+)', span.text)
                    if match:
                        total_count = int(match.group(1))
                        print("总页数为:", total_count)
                    else:
                        print("未匹配到页码格式。")
                else:
                    print("未找到包含页码的 <span> 标签。")
            else:
                print("未找到 id='page' 的 div。")
                 
                 
        # 获取 gallerypic 区域
        gallery_div = soup.find('div', class_='gallerypic')
 
        if not gallery_div:
            return items
 
      
        # 获取 class 为 gallery_jieshao 的 div
        jieshao_div = soup.find('div', class_='gallery_jieshao')
 
        # 从中提取 h1 的内容
        if jieshao_div:
            title = jieshao_div.find('h1').get_text(strip=True)
            #print("图集标题:", title)
         
        type_author = [a.get_text(strip=True) for a in soup.select('.gallery_renwu_title a')]
      
      
        first_img = gallery_div.find('img')
        # if first_img and first_img.has_attr('src'):
        #     print("第一个图片地址:", first_img['src'])
        # else:
        #     print("未找到 img 标签")
             
        item = {
            "img": first_img["src"] if first_img else "",
            "ztitle": title,
            "ztitle_href": url,
            "author": type_author[1],
            "rtitle": type_author[0],
            "count":str(total_count)
        }    
             
        items.append(item)    
 
        return items    
         
         
    except Exception as e:
        result = "请求失败:" + str(e)
        print(result)
        return items
     
 
 
  
def crawl_all_pages(search_url: str, limit: int = -1, progress_callback=None) -> List[Dict[str, str]]:
    """
    分页抓取所有图片组信息
    """
    all_results = []
    page = 0
      
    if "searchid" in search_url:
        searchid_match = re.search(r"searchid=(\d+)", search_url)
        if not searchid_match:
            print("无法提取 searchid")
            return []
  
        searchid = searchid_match.group(1)
  
        while True:
            # 构造分页 URL
            if page == 0:
                page_url = search_url
            else:
                page_url = f"{BASE_URL}/e/search/result/index.php?page={page}&searchid={searchid}"
  
            print(f"\n[抓取第 {page + 1} 页] {page_url}")
  
            try:
                response = requests.get(page_url, timeout=10)
                soup = BeautifulSoup(response.text, "html.parser")
            except Exception as e:
                print("请求失败:", e)
                break
  
            if page == 0:
                total = get_total_count(soup)
                print(f"[总计] 页面声明图片组总数: {total}")
  
            results = parse_gallery_items_from_root(soup)
            if not results:
                print("[结束] 当前页无数据，提前结束。")
                break

            # Apply limit if specified
            if limit > 0:
                remaining_slots = limit - len(all_results)
                if remaining_slots <= 0:
                    print(f"[限制] 已达到设定的结果限制 {limit}，停止抓取。")
                    break
                elif len(results) > remaining_slots:
                    results = results[:remaining_slots]
                    print(f"[限制] 截取到 {remaining_slots} 项以满足限制。")

            all_results.extend(results)

            # Call progress callback if provided
            if progress_callback:
                progress_callback(len(all_results), total)

            # 打印当前页的结果
            #for i, item in enumerate(results, 1):
            #    print(f"  第 {len(all_results) - len(results) + i} 项:")
            #    print(f"    缩略图: {item['img']}")
            #    print(f"    主标题: {item['ztitle']}")
            #    print(f"    分类  : {item['rtitle']}")
            #    print(f"    链接  : {item['ztitle_href']}")
            #

            # Check if we've reached the limit or total
            if limit > 0 and len(all_results) >= limit:
                print(f"[完成] 已达到设定的结果限制 {limit}。")
                break
            elif len(all_results) >= total:
                print("[完成] 已抓取全部项目。")
                break
  
            page += 1
  
        return all_results
          
    else:
        search_url = re.sub(r'_\d+\.html$', '.html', search_url)
        response = requests.get(search_url)
        if not response:
            return "", []
          
        soup = BeautifulSoup(response.text, "html.parser")
  
        # 获取总页数
        page_div = soup.find("div", class_="layui-box layui-laypage layui-laypage-default")
        total_pages = 1
        if page_div:
            span = page_div.find("span")
            if span:
                match = re.search(r'\d+/(\d+)', span.text.strip())
                if match:
                    total_pages = int(match.group(1))
                    print(f"总页数：{total_pages}")
  
        for index in range(1,total_pages+1):
            page_url = re.sub(r'\.html$', f'_{index}.html', search_url)

            print(f"\n[抓取第 {index + 1} 页] {page_url}")

            try:
                response = requests.get(page_url, timeout=10)
                soup = BeautifulSoup(response.text, "html.parser")
            except Exception as e:
                print("请求失败:", e)
                break

            results = parse_gallery_items_from_root(soup)
            if not results:
                print("[结束] 当前页无数据，提前结束。")
                break

            # Apply limit if specified
            if limit > 0:
                remaining_slots = limit - len(all_results)
                if remaining_slots <= 0:
                    print(f"[限制] 已达到设定的结果限制 {limit}，停止抓取。")
                    break
                elif len(results) > remaining_slots:
                    results = results[:remaining_slots]
                    print(f"[限制] 截取到 {remaining_slots} 项以满足限制。")

            all_results.extend(results)

            # Call progress callback if provided
            if progress_callback:
                progress_callback(len(all_results), None)

            # Check if we've reached the limit
            if limit > 0 and len(all_results) >= limit:
                print(f"[完成] 已达到设定的结果限制 {limit}。")
                break
  
        return all_results                    
  
# --- PySide6 GUI ---
  
class GalleryCrawler(QWidget):
    def __init__(self, cookies=None):
        super().__init__()
        self.setWindowTitle("写真专辑下载")
        self.resize(900, 600)

        # Initialize attributes before init_ui()
        self.download_queue = deque()
        self.current_worker = None
        self.cancel_requested = False
        self.selected_items = None
        self.is_downloading = False

        # Multi-select state management
        self.checked_items = set()  # Track checked item indices
        self.select_all_state = False  # Track select all checkbox state

        # Search limit settings
        self.search_limit_options = [10, 20, 50, 100, -1]  # -1 means "All"
        self.search_limit_labels = ["10", "20", "50", "100", "全部"]

        # Download settings
        self.max_workers = 4  # Default concurrent download threads
        self.download_timeout = 30  # Download timeout in seconds

        # Settings persistence
        self.settings = QSettings("PicDownloader", "GalleryCrawler")
        self.search_limit = self.load_search_limit()
        self.load_download_settings()

        # Download progress manager
        self.progress_manager = DownloadProgressManager()

        # Initialize UI after all attributes are set
        self.init_ui()

        # Check for incomplete downloads on startup (with delay to ensure UI is ready)
        QApplication.processEvents()  # Process any pending events
        self.check_incomplete_downloads()
  
        self.cookies = cookies
        self.headers = self._default_headers()
        self.session = self._create_optimized_session()
        if cookies:
            self.session.cookies.update(cookies)

    def load_search_limit(self):
        """Load search limit from settings"""
        saved_limit = self.settings.value("search_limit", 50, type=int)
        # Ensure the saved limit is valid
        if saved_limit in self.search_limit_options:
            return saved_limit
        return 50  # Default fallback

    def load_download_settings(self):
        """Load download settings from settings"""
        self.max_workers = self.settings.value("max_workers", 4, type=int)
        self.download_timeout = self.settings.value("download_timeout", 30, type=int)
        # Ensure reasonable limits
        self.max_workers = max(1, min(self.max_workers, 16))  # 1-16 threads
        self.download_timeout = max(10, min(self.download_timeout, 120))  # 10-120 seconds

    def save_download_settings(self):
        """Save download settings to settings"""
        self.settings.setValue("max_workers", self.max_workers)
        self.settings.setValue("download_timeout", self.download_timeout)
        self.settings.sync()
  
    def init_ui(self):
        layout = QVBoxLayout(self)
        main_layout = QHBoxLayout()  # 水平主布局
        # 左侧竖直布局（搜索条 + TreeView + 按钮）
        left_layout = QVBoxLayout()
  
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入关键词搜索或直接粘贴网址...")
        self.search_input.setText("")

        # Search limit controls
        limit_label = QLabel("结果限制:")
        self.limit_combo = QComboBox()
        for i, label in enumerate(self.search_limit_labels):
            self.limit_combo.addItem(label, self.search_limit_options[i])
        # Set default to saved limit
        try:
            default_index = self.search_limit_options.index(self.search_limit)
            self.limit_combo.setCurrentIndex(default_index)
        except ValueError:
            # If saved limit is not in options, default to 50
            default_index = self.search_limit_options.index(50)
            self.limit_combo.setCurrentIndex(default_index)
            self.search_limit = 50
        self.limit_combo.currentIndexChanged.connect(self.on_limit_changed)
        self.limit_combo.setToolTip("设置搜索结果数量限制以提高加载速度")

        self.search_btn = QPushButton("搜索")
        self.search_btn.clicked.connect(self.start_search)

        search_layout.addWidget(self.search_input)
        search_layout.addWidget(limit_label)
        search_layout.addWidget(self.limit_combo)
        search_layout.addWidget(self.search_btn)
  
        self.tree = QTreeWidget()
        self.tree.setHeaderLabels(["选择", "序号", "主标题", "分类", "链接","数量","作者"])
        self.tree.setColumnWidth(0, 60)   # 选择列
        self.tree.setColumnWidth(1, 50)   # 序号
        self.tree.setColumnWidth(2, 320)  # 主标题 (稍微缩小)
        self.tree.setColumnWidth(3, 150)  # 分类
        self.tree.setColumnWidth(4, 280)  # 链接 (稍微缩小)
        self.tree.setColumnWidth(5, 50)   # 数量
        self.tree.setColumnWidth(6, 100)  # 作者

        self.tree.itemSelectionChanged.connect(self.on_tree_selection_changed)  # 监听选中变化
  
        # Multi-select controls
        multiselect_layout = QHBoxLayout()
        self.select_all_checkbox = QCheckBox("全选")
        self.select_all_checkbox.clicked.connect(self.toggle_select_all)

        # Set consistent style for select all checkbox
        self.select_all_checkbox.setStyleSheet("""
            QCheckBox {
                spacing: 5px;
                font-weight: bold;
            }
            QCheckBox::indicator {
                width: 22px;
                height: 22px;
                border: 3px solid #cccccc;
                border-radius: 5px;
                background-color: white;
            }
            QCheckBox::indicator:hover {
                border: 3px solid #0078d4;
                background-color: #f0f8ff;
            }
            QCheckBox::indicator:checked {
                border: 3px solid #0078d4;
                background-color: #0078d4;
                image: none;
            }
            QCheckBox::indicator:checked:hover {
                border: 3px solid #106ebe;
                background-color: #106ebe;
            }
        """)

        self.selection_counter = QLabel("已选择: 0 项")
        self.selection_counter.setStyleSheet("font-weight: bold; color: #0066cc;")

        multiselect_layout.addWidget(self.select_all_checkbox)
        multiselect_layout.addWidget(self.selection_counter)
        multiselect_layout.addStretch()  # 推到左边

        # Download buttons
        btn_layout = QHBoxLayout()
        self.btn_download_selected = QPushButton("下载选中项")
        self.btn_download_selected.clicked.connect(self.download_selected_items)
        self.btn_download_selected.setEnabled(False)  # 初始禁用，直到有选中项

        self.btn_download_all = QPushButton("下载全部")
        self.btn_download_all.clicked.connect(self.download_all)

        self.btn_cancel_download = QPushButton("取消下载")
        self.btn_cancel_download.clicked.connect(self.cancel_download)
        self.btn_cancel_download.setEnabled(False)  # 初始禁用

        self.btn_show_all_more = QPushButton("显示缩略图")
        self.btn_show_all_more.clicked.connect(self.show_allthumbnails)

        self.btn_settings = QPushButton("下载设置")
        self.btn_settings.clicked.connect(self.show_download_settings)

        self.btn_resume = QPushButton("恢复下载")
        self.btn_resume.clicked.connect(self.manual_resume_download)
        self.btn_resume.setToolTip("手动检查并恢复未完成的下载任务")

        btn_layout.addWidget(self.btn_download_selected)
        btn_layout.addWidget(self.btn_download_all)
        btn_layout.addWidget(self.btn_cancel_download)
        btn_layout.addWidget(self.btn_show_all_more)
        btn_layout.addWidget(self.btn_settings)
        btn_layout.addWidget(self.btn_resume)
  
        left_layout.addLayout(search_layout)
        left_layout.addWidget(self.tree)
        left_layout.addLayout(multiselect_layout)
        left_layout.addLayout(btn_layout)
  
        right_layout = QVBoxLayout()
          
        # 右侧显示缩略图
        self.image_label = QLabel("选中项缩略图显示区域")
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setMinimumWidth(320)
        self.image_label.setStyleSheet("border: 1px solid gray;")
          
        self.link_label = QLabel()
        self.link_label.setText('<a href="https://www.mxd009.cc/">在线浏览</a>')
        self.link_label.setOpenExternalLinks(True)  # &#9989; 允许打开外部链接
        self.link_label.setTextInteractionFlags(Qt.TextBrowserInteraction)  # 可点击
        self.link_label.setAlignment(Qt.AlignCenter)
          
        # 显示更多按钮
        self.btn_show_more = QPushButton("显示更多缩略图")
        self.btn_show_more.clicked.connect(self.show_thumbnails)
        self.btn_show_more.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
          
        right_layout.addWidget(self.image_label, stretch=1)
        right_layout.addWidget(self.link_label, stretch=0)
        right_layout.addWidget(self.btn_show_more, stretch=0, alignment=Qt.AlignHCenter)
  
        # 底部添加进度条
        bottom_layout = QHBoxLayout()
        self.album_label = QLabel()
        self.album_label.setText('')
          
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("下载进度：%p%")
          
        bottom_layout.addWidget(self.album_label, stretch=0)
        bottom_layout.addWidget(self.progress_bar, stretch=1)
  
        # 添加左右布局
        main_layout.addLayout(left_layout, 3)  # 左边占3份
        main_layout.addLayout(right_layout, 1)  # 右边占1份
          
        layout.addLayout(main_layout)
        # 此处添加进度条
        layout.addLayout(bottom_layout)
          
    def _default_headers(self):
        """Default headers to mimic browser behavior"""
        return {
            "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": "zh-CN,zh;q=0.9",
            "cache-control": "max-age=0",
            "sec-ch-ua": '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "document",
            "sec-fetch-mode": "navigate",
            "sec-fetch-site": "same-origin",
            "upgrade-insecure-requests": "1",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        }

    def _create_optimized_session(self):
        """Create an optimized requests session for better performance"""
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry

        session = requests.Session()
        session.headers.update(self.headers)

        # Configure retry strategy
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )

        # Configure HTTP adapter with connection pooling
        adapter = HTTPAdapter(
            pool_connections=20,  # Number of connection pools
            pool_maxsize=20,      # Maximum number of connections in pool
            max_retries=retry_strategy,
            pool_block=False
        )

        session.mount("http://", adapter)
        session.mount("https://", adapter)

        return session

    # Multi-select functionality methods
    def toggle_select_all(self):
        """Toggle select all/deselect all functionality"""
        self.select_all_state = self.select_all_checkbox.isChecked()
        total_items = self.tree.topLevelItemCount()



        # Clear or fill the checked_items set based on select all state
        if self.select_all_state:
            # Select all items
            self.checked_items.clear()
            for i in range(total_items):
                item = self.tree.topLevelItem(i)
                checkbox = self.tree.itemWidget(item, 0)
                if checkbox:
                    checkbox.setChecked(True)
                    self.checked_items.add(i)

        else:
            # Deselect all items
            for i in range(total_items):
                item = self.tree.topLevelItem(i)
                checkbox = self.tree.itemWidget(item, 0)
                if checkbox:
                    checkbox.setChecked(False)
            self.checked_items.clear()


        self.update_selection_counter()
        self.update_download_button_state()

    def on_item_checkbox_changed(self, item_index):
        """Handle individual item checkbox state change"""
        item = self.tree.topLevelItem(item_index)
        if not item:
            return

        checkbox = self.tree.itemWidget(item, 0)
        if not checkbox:
            return

        if checkbox.isChecked():
            self.checked_items.add(item_index)
        else:
            self.checked_items.discard(item_index)
            # If any item is unchecked, uncheck select all
            self.select_all_checkbox.setChecked(False)
            self.select_all_state = False

        # Check if all items are selected to update select all checkbox
        if len(self.checked_items) == self.tree.topLevelItemCount() and self.tree.topLevelItemCount() > 0:
            self.select_all_checkbox.setChecked(True)
            self.select_all_state = True

        self.update_selection_counter()
        self.update_download_button_state()

    def update_selection_counter(self):
        """Update the selection counter label"""
        count = len(self.checked_items)
        self.selection_counter.setText(f"已选择: {count} 项")

    def update_download_button_state(self):
        """Enable/disable download selected button based on selection"""
        has_selection = len(self.checked_items) > 0
        self.btn_download_selected.setEnabled(has_selection and not self.is_downloading)

    def get_selected_items_data(self):
        """Get data for all selected items"""
        selected_data = []
        for item_index in sorted(self.checked_items):
            if item_index < self.tree.topLevelItemCount():
                item = self.tree.topLevelItem(item_index)
                data = {
                    'index': item.text(1),  # 序号 (column 1 now)
                    'title': item.text(2),  # 主标题 (column 2 now)
                    'category': item.text(3),  # 分类 (column 3 now)
                    'url': item.text(4),  # 链接 (column 4 now)
                    'count': int(item.text(5)),  # 数量 (column 5 now)
                    'author': item.text(6),  # 作者 (column 6 now)
                    'img_url': item.data(0, Qt.ItemDataRole.UserRole + 1)  # 缩略图URL
                }
                selected_data.append(data)
        return selected_data

    def clear_selection_state(self):
        """Clear all selection state when loading new results"""
        self.checked_items.clear()
        self.select_all_checkbox.setChecked(False)
        self.select_all_state = False
        self.update_selection_counter()
        self.update_download_button_state()

    # Settings management methods
    def save_search_limit(self):
        """Save current search limit to settings"""
        self.settings.setValue("search_limit", self.search_limit)
        self.settings.sync()

    def on_limit_changed(self):
        """Handle search limit combo box change"""
        current_data = self.limit_combo.currentData()
        if current_data is not None:
            self.search_limit = current_data
            self.save_search_limit()
            # Update UI to show current limit
            self.update_search_status()

    def update_search_status(self, current_count=0, total_count=None):
        """Update search status display"""
        if self.search_limit == -1:
            limit_text = "全部"
        else:
            limit_text = str(self.search_limit)

        if current_count > 0:
            if total_count is not None:
                status = f"正在加载 {current_count}/{total_count} 项 (限制: {limit_text})"
            else:
                status = f"已加载 {current_count} 项 (限制: {limit_text})"
        else:
            status = f"搜索限制: {limit_text} 项"

        if not self.is_downloading:
            self.set_album(status)

    def show_download_settings(self):
        """Show download settings dialog"""
        dialog = DownloadSettingsDialog(self.max_workers, self.download_timeout, self)
        if dialog.exec() == QDialog.Accepted:
            self.max_workers, self.download_timeout = dialog.get_settings()
            self.save_download_settings()
            QMessageBox.information(self, "设置", f"下载设置已更新：\n并发线程数: {self.max_workers}\n超时时间: {self.download_timeout}秒")

    def manual_resume_download(self):
        """手动检查并恢复下载"""
        incomplete_sessions = self.progress_manager.get_incomplete_sessions()
        if incomplete_sessions:
            self.show_resume_dialog(incomplete_sessions)
        else:
            QMessageBox.information(self, "提示", "没有发现未完成的下载任务。")

    def check_incomplete_downloads(self):
        """检查是否有未完成的下载任务"""
        incomplete_sessions = self.progress_manager.get_incomplete_sessions()
        if incomplete_sessions:
            self.show_resume_dialog(incomplete_sessions)

    def show_resume_dialog(self, incomplete_sessions):
        """显示恢复下载对话框"""
        dialog = ResumeDownloadDialog(incomplete_sessions, self)
        if dialog.exec() == QDialog.Accepted:
            selected_session = dialog.get_selected_session()
            if selected_session:
                self.resume_download_session(selected_session)

    def resume_download_session(self, session_data):
        """恢复下载会话"""
        # 加载会话到进度管理器
        self.progress_manager.current_session = session_data

        # 获取待下载项目
        pending_items = session_data.get("pending_items", [])
        if not pending_items:
            QMessageBox.information(self, "提示", "该会话没有待下载的项目。")
            return

        # 验证文件完整性并过滤已完成的项目
        verified_items = self.verify_and_filter_items(pending_items)

        if not verified_items:
            QMessageBox.information(self, "提示", "所有项目都已下载完成。")
            self.progress_manager.update_session_status("completed")
            return

        # 更新待下载列表
        self.progress_manager.current_session["pending_items"] = verified_items
        self.progress_manager.save_progress()

        # 开始恢复下载
        self.start_resume_download(verified_items)

        # 显示恢复信息
        stats = self.progress_manager.get_session_stats()
        QMessageBox.information(self, "恢复下载",
            f"正在恢复下载会话：{session_data['session_name']}\n"
            f"总计: {stats['total']} 项\n"
            f"已完成: {stats['completed']} 项\n"
            f"待下载: {len(verified_items)} 项")

    def verify_and_filter_items(self, items):
        """验证文件完整性并过滤已完成的项目"""
        verified_items = []
        for item in items:
            if not self.is_album_complete(item):
                verified_items.append(item)
        return verified_items

    def is_album_complete(self, item_data):
        """检查专辑是否已完整下载"""
        try:
            album_title = item_data.get("title", "")
            author = item_data.get("author", "")
            expected_count = item_data.get("count", 0)

            # 构建专辑目录路径
            album_dir = os.path.join(author, album_title) if author else album_title

            if not os.path.exists(album_dir):
                return False

            # 计算已下载的文件数量
            downloaded_files = [f for f in os.listdir(album_dir)
                              if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp'))]

            # 如果下载的文件数量达到预期，认为已完成
            return len(downloaded_files) >= expected_count

        except Exception as e:
            print(f"检查专辑完整性失败: {e}")
            return False

    def start_resume_download(self, items):
        """开始恢复下载"""
        # 清空当前下载队列
        self.download_queue.clear()
        self.cancel_requested = False

        # 添加待下载项目到队列
        for item in items:
            self.download_queue.append((
                item.get("index", ""),
                item.get("author", ""),
                item.get("title", ""),
                item.get("url", ""),
                item.get("count", 0)
            ))

        # 更新UI状态
        self.btn_cancel_download.setEnabled(True)
        self.btn_download_selected.setEnabled(False)
        self.btn_download_all.setEnabled(False)
        self.search_btn.setEnabled(False)

        # 开始下载
        self.progress_bar.setValue(0)
        self.start_next_download()
      
    def show_allthumbnails(self):
        img_list = []
        for i in range(self.tree.topLevelItemCount()):
            item = self.tree.topLevelItem(i)
            img = item.data(0, Qt.ItemDataRole.UserRole + 1)
            if img:
                img_list.append(img)
  
        # 去重并保留顺序
        img_urls = list(dict.fromkeys(img_list))
  
        if len(img_urls) == 0:
            QMessageBox.warning(self, "提示", "请先搜索")
            return
  
        # 创建实例
        worker = DownloadWorker(self.session, "", "", img_urls, 0, self.max_workers, self.download_timeout)
        dialog = ThumbnailViewer(img_urls, self)
        dialog.exec()  
      
    def show_thumbnails(self):
        selected_item = self.tree.currentItem()
        if selected_item is None:
            QMessageBox.warning(self, "提示", "请先选择一项")
            return

        img_url = selected_item.data(0, Qt.ItemDataRole.UserRole + 1)
        album_index = selected_item.text(1)  # 序号现在在第1列
        album_title = selected_item.text(2)  # 主标题现在在第2列
        gallery_url = selected_item.text(4)  # 链接现在在第4列
        total_count = int(selected_item.text(5))  # 数量现在在第5列
        author = selected_item.text(6)  # 作者现在在第6列
        # 创建实例
        worker = DownloadWorker(self.session, author, album_title, gallery_url, total_count, self.max_workers, self.download_timeout)

        # 调用方法获取图片链接
        image_urls = worker.get_image_urls()
        dialog = ThumbnailViewer(image_urls, self)
        dialog.exec()
          
    def cancel_download(self):
        self.cancel_requested = True
        self.download_queue.clear()  # 清除后续任务
  
        if self.current_worker and self.current_worker.isRunning():
            self.current_worker.terminate()  # 强行终止当前线程（&#9888;&#65039; 不推荐用于长期任务）
            self.current_worker.wait()
  
        self.progress_bar.setValue(0)
        self.is_downloading = False
        self.btn_cancel_download.setEnabled(False)
        self.btn_download_all.setEnabled(True)
        self.update_download_button_state()  # Update download selected button based on selection
        self.search_btn.setEnabled(True)

        self.enable_search()
        QMessageBox.information(self, "提示", "下载任务已被取消。")
        self.set_album("")
      
    def set_album(self, value:str):
        def update():
            self.album_label.setText(value)
        self.run_on_main_thread(update)
      
    def set_progress(self, value: int):
        def update():
            self.progress_bar.setValue(value)
        self.run_on_main_thread(update)

    def update_speed_info(self, speed_info: str):
        """Update progress bar format with speed information"""
        def update():
            self.progress_bar.setFormat(f"下载进度：%p% | {speed_info}")
        self.run_on_main_thread(update)
      
    def on_tree_selection_changed(self):
        selected_items = self.tree.selectedItems()
        if not selected_items:
            self.image_label.setText("选中项缩略图显示区域")
            self.image_label.setPixmap(QPixmap())  # 清空图片
            self.link_label.setText('<a href="https://www.mxd009.cc/">在线浏览</a>')
            return

        item = selected_items[0]
        img_url = item.data(0, Qt.ItemDataRole.UserRole + 1)
        album_index = item.text(1)  # 序号现在在第1列
        album_title = item.text(2)  # 主标题现在在第2列

        if not self.is_downloading:
            self.set_album(album_index + " " + album_title)
            self.set_progress(0)

        if not img_url:
            self.image_label.setText("无缩略图")
            self.image_label.setPixmap(QPixmap())
            self.link_label.setText('<a href="https://www.mxd009.cc/">在线浏览</a>')
            return

        self.link_label.setText(f'<a href="{item.text(4)}">在线浏览</a>')  # 链接现在在第4列
        # 异步加载图片避免卡界面
        threading.Thread(target=self.load_image_from_url, args=(img_url,), daemon=True).start()
  
    def load_image_from_url(self, url):
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            data = response.content
        except Exception:
            self.run_on_main_thread(lambda: self.image_label.setText("加载图片失败"))
            return
  
        pixmap = QPixmap()
        pixmap.loadFromData(data)
        scaled = pixmap.scaled(self.image_label.size(), Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
  
        def set_pix():
            self.image_label.setPixmap(scaled)
            self.image_label.setText("")
  
        self.run_on_main_thread(set_pix)
  
    def start_search(self):
        keyword = self.search_input.text().strip()
        if not keyword:
            QMessageBox.warning(self, "提示", "请输入关键词！")
            return
        if keyword.startswith("https://www.mxd009.cc") and "searchid" not in keyword  and "/gallery" not in keyword:
            threading.Thread(target=self.search_by_url, args=(keyword,), daemon=True).start()
        elif keyword.startswith("https://www.mxd009.cc") and "/gallery" in keyword:
            threading.Thread(target=self.search_by_gallery, args=(keyword,), daemon=True).start()
        else:
            threading.Thread(target=self.search_and_load, args=(keyword,), daemon=True).start()
              
        self.search_btn.setEnabled(False)
        self.tree.clear()
        if not self.is_downloading:
            self.update_search_status()  # Show current limit setting
            self.set_progress(0)
        self.selected_items = None
        self.clear_selection_state()  # Clear multi-select state
        self.image_label.setText("选中项缩略图显示区域")
         
    def search_by_gallery(self,search_url):
        results = crawl_single_gallery(search_url)
        if not results:
            self.show_message("未找到任何结果。")
            self.enable_search()
            return
        self.load_results(results)
        self.enable_search()
              
    def search_and_load(self, keyword):
        search_url = submit_search(keyword)
        if not search_url:
            self.show_message("搜索失败，未获取有效跳转链接。")
            self.enable_search()
            return
        self.search_by_url(search_url)
          
    def search_by_url(self,search_url):
        # Create progress callback
        def progress_callback(current, total):
            self.run_on_main_thread(lambda: self.update_search_status(current, total))

        results = crawl_all_pages(search_url, self.search_limit, progress_callback)
        if not results:
            self.show_message("未找到任何结果。")
            self.enable_search()
            return
        self.load_results(results)
        self.enable_search()
  
    def load_results(self, results):
        def update_ui():
            self.tree.clear()
            self.clear_selection_state()  # Clear selection state when loading new results

            for idx, item in enumerate(results, 1):
                tree_item = QTreeWidgetItem([
                    "",  # 选择列 - 空白，将放置checkbox
                    str(idx),  # 序号
                    item["ztitle"],  # 主标题
                    item["rtitle"],  # 分类
                    item["ztitle_href"],  # 链接
                    item["count"],  # 数量
                    item["author"]  # 作者
                ])
                tree_item.setData(0, Qt.ItemDataRole.UserRole + 1, item["img"])
                self.tree.addTopLevelItem(tree_item)

                # Create checkbox for this item
                checkbox = QCheckBox()
                checkbox.setEnabled(True)  # Ensure checkbox is enabled
                checkbox.setCheckable(True)  # Ensure checkbox is checkable

                # Set checkbox style for better visibility
                checkbox.setStyleSheet("""
                    QCheckBox {
                        spacing: 5px;
                    }
                    QCheckBox::indicator {
                        width: 22px;
                        height: 22px;
                        border: 3px solid #cccccc;
                        border-radius: 5px;
                        background-color: white;
                    }
                    QCheckBox::indicator:hover {
                        border: 3px solid #0078d4;
                        background-color: #f0f8ff;
                    }
                    QCheckBox::indicator:checked {
                        border: 3px solid #0078d4;
                        background-color: #0078d4;
                        image: none;
                    }
                    QCheckBox::indicator:checked:hover {
                        border: 3px solid #106ebe;
                        background-color: #106ebe;
                    }
                """)

                # Add text to show check state more clearly
                checkbox.setText("")  # No text, just the indicator

                # Use default parameter to capture the current value of idx-1
                item_index = idx - 1
                checkbox.clicked.connect(lambda checked=False, index=item_index: self.on_item_checkbox_changed(index))
                self.tree.setItemWidget(tree_item, 0, checkbox)

            # Update status to show final count
            self.update_search_status(len(results))

        self.run_on_main_thread(update_ui)
  
    def download_selected_items(self):
        """Download all selected items using checkboxes"""
        if self.is_downloading:
            QMessageBox.information(self, "提示", "当前任务正在下载中，请等待完成。")
            return

        selected_data = self.get_selected_items_data()
        if not selected_data:
            QMessageBox.information(self, "提示", "请先选择要下载的项。")
            return

        # Create download session
        session_name = f"批量下载_{len(selected_data)}项_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        session_id = self.progress_manager.create_session(session_name, len(selected_data))

        # Add items to progress manager
        self.progress_manager.add_pending_items(selected_data)

        # Setup batch download
        self.download_queue.clear()
        self.cancel_requested = False
        self.btn_cancel_download.setEnabled(True)
        self.btn_download_selected.setEnabled(False)
        self.btn_download_all.setEnabled(False)
        self.search_btn.setEnabled(False)

        # Add selected items to download queue
        for data in selected_data:
            self.download_queue.append((
                data['index'],
                data['author'],
                data['title'],
                data['url'],
                data['count']
            ))

        self.progress_bar.setValue(0)
        self.start_next_download()

    def download_selected(self):
        """Legacy method for single item download (keeping for compatibility)"""
        if self.is_downloading:
            QMessageBox.information(self, "提示", "当前任务正在下载中，请等待完成。")
            return

        items = self.tree.selectedItems()
        if not items:
            QMessageBox.information(self, "提示", "请先选中要下载的项。")
            return

        album_index = items[0].text(1)  # 序号现在在第1列
        album_title = items[0].text(2)  # 主标题现在在第2列
        download_url = items[0].text(4)  # 链接现在在第4列
        total_count = int(items[0].text(5))  # 数量现在在第5列
        author = items[0].text(6)  # 作者现在在第6列

        self.is_downloading = True
        self.progress_bar.setValue(0)
        self.set_album(album_index + " " + album_title)
        self.worker = DownloadWorker(self.session, author, album_title, download_url, total_count, self.max_workers, self.download_timeout)
        self.worker.progress.connect(self.set_progress)
        self.worker.speed_update.connect(self.update_speed_info)

        def finish_handler(msg):
            QMessageBox.information(self, "完成", msg)
            self.is_downloading = False
            self.update_download_button_state()  # Update download selected button based on selection
            self.btn_download_all.setEnabled(True)
            self.search_btn.setEnabled(True)

        self.worker.finished.connect(finish_handler)
        self.worker.message.connect(lambda err: QMessageBox.critical(self, "错误", err))
        self.worker.start()
 
          
    def download_all(self):
        if self.is_downloading:
            QMessageBox.information(self, "提示", "当前任务正在下载中，请等待完成。")
            return
        count = self.tree.topLevelItemCount()
        if count == 0:
            QMessageBox.information(self, "提示", "无数据可下载。")
            return

        # Prepare all items data
        all_items = []
        for i in range(count):
            item = self.tree.topLevelItem(i)
            item_data = {
                'index': item.text(1),  # 序号现在在第1列
                'title': item.text(2),  # 主标题现在在第2列
                'category': item.text(3),  # 分类现在在第3列
                'url': item.text(4),  # 链接现在在第4列
                'count': int(item.text(5)),  # 数量现在在第5列
                'author': item.text(6),  # 作者现在在第6列
                'img_url': item.data(0, Qt.ItemDataRole.UserRole + 1)  # 缩略图URL
            }
            all_items.append(item_data)

        # Create download session
        session_name = f"全部下载_{count}项_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        session_id = self.progress_manager.create_session(session_name, count)

        # Add items to progress manager
        self.progress_manager.add_pending_items(all_items)

        self.download_queue.clear()
        self.cancel_requested = False
        self.btn_cancel_download.setEnabled(True)
        self.btn_download_all.setEnabled(False)
        self.search_btn.setEnabled(False)

        for item_data in all_items:
            self.download_queue.append((
                item_data['index'],
                item_data['author'],
                item_data['title'],
                item_data['url'],
                item_data['count']
            ))

        self.progress_bar.setValue(0)
        self.start_next_download()
  
    def start_next_download(self):
        if self.cancel_requested:
            QMessageBox.information(self, "取消", "下载已取消。")
            self.btn_cancel_download.setEnabled(False)
            self.btn_download_all.setEnabled(True)
            self.search_btn.setEnabled(True)
            self.enable_search()
            return
          
        if not self.download_queue:
            # Mark session as completed
            if self.progress_manager.current_session:
                self.progress_manager.update_session_status("completed")

            QMessageBox.information(self, "完成", "全部下载完成！")
            self.is_downloading = False
            self.btn_cancel_download.setEnabled(False)
            self.btn_download_all.setEnabled(True)
            self.update_download_button_state()  # Update download selected button based on selection
            self.search_btn.setEnabled(True)
            self.enable_search()
            return
  
        album_index,author, album_title, url, total_count = self.download_queue.popleft()

        # Update progress manager with current item
        if self.progress_manager.current_session:
            current_item_data = {
                'index': album_index,
                'author': author,
                'title': album_title,
                'url': url,
                'count': total_count
            }
            self.progress_manager.set_current_item(current_item_data)

        self.progress_bar.setValue(0)
        self.set_album(album_index + " " + album_title)
        self.current_worker = DownloadWorker(self.session, author, album_title, url, total_count, self.max_workers, self.download_timeout)
        self.current_worker.progress.connect(self.set_progress)
        self.current_worker.speed_update.connect(self.update_speed_info)
        self.current_worker.message.connect(lambda err: QMessageBox.critical(self, "错误", err))

        def on_finished(msg):
            # Mark item as completed or failed in progress manager
            if self.progress_manager.current_session:
                current_item = self.progress_manager.current_session.get("current_item")
                if current_item:
                    if "成功" in msg or "完成" in msg:
                        self.progress_manager.mark_item_completed(current_item)
                    else:
                        self.progress_manager.mark_item_failed(current_item, msg)

            logger.info(msg)
            self.start_next_download()

        self.current_worker.finished.connect(on_finished)
        self.current_worker.start()
  
    def show_message(self, text):
        def msg():
            QMessageBox.information(self, "提示", text)
        self.run_on_main_thread(msg)
  
    def enable_search(self):
        def en():
            self.search_btn.setEnabled(True)
        self.run_on_main_thread(en)
  
    def run_on_main_thread(self, func):
        # PySide6 主线程调用
        QApplication.instance().postEvent(self, _FuncEvent(func))
  
    def customEvent(self, event):
        event.func()
  
  
class DownloadWorker(QThread):
    progress = Signal(int)
    finished = Signal(str)
    message = Signal(str)
    speed_update = Signal(str)  # New signal for speed updates

    def __init__(self, session, album_title, title, url, total_count, max_workers=4, timeout=30):
        super().__init__()
        self.session = session
        self.album_title = album_title
        self.title = title
        self.url = url
        self.total_count = total_count
        self.max_workers = max_workers
        self.timeout = timeout
        self.progress_lock = Lock()
        self.success_count = 0
        self.start_time = None
        self.downloaded_bytes = 0
  
    def run(self):
        try:
            success = self.process_album()
            if success:
                self.finished.emit(f"{self.title} 下载完成")
            else:
                self.finished.emit(f"{self.title} 下载失败")
        except Exception as e:
            self.message.emit(f"{self.title} 异常: {e}")
  
    def safe_request(self, url, timeout=10):
        try:
            response = self.session.get(url, timeout=timeout)
            response.raise_for_status()
            response.encoding = response.apparent_encoding
            return response
        except Exception:
            return None
  
    def download_image(self, url, filepath):
        """Download a single image with improved error handling and resume support"""
        # Check if file already exists and is complete
        if os.path.exists(filepath) and os.path.getsize(filepath) > 0:
            return True

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        temp_filepath = filepath + ".tmp"
        resume_pos = 0

        # Check if partial download exists
        if os.path.exists(temp_filepath):
            resume_pos = os.path.getsize(temp_filepath)

        try:
            headers = {}
            if resume_pos > 0:
                headers['Range'] = f'bytes={resume_pos}-'

            response = self.session.get(url, timeout=self.timeout, stream=True, headers=headers)
            response.raise_for_status()

            # Open file in append mode if resuming, otherwise write mode
            mode = "ab" if resume_pos > 0 else "wb"
            file_size = resume_pos

            with open(temp_filepath, mode) as f:
                for chunk in response.iter_content(chunk_size=32768):  # Even larger chunk size
                    if chunk:  # Filter out keep-alive chunks
                        f.write(chunk)
                        file_size += len(chunk)

            # Update downloaded bytes counter
            with self.progress_lock:
                self.downloaded_bytes += (file_size - resume_pos)

            # Rename temp file to final name
            os.rename(temp_filepath, filepath)
            return True

        except Exception as e:
            # Don't delete temp file on error - allow resume next time
            return False

    def download_single_image(self, args):
        """Download a single image (for use with ThreadPoolExecutor)"""
        url, filepath, index = args
        success = self.download_image(url, filepath)

        # Thread-safe progress update
        with self.progress_lock:
            if success:
                self.success_count += 1
            progress_percent = int(self.success_count / self.total_count * 100)
            self.progress.emit(progress_percent)

            # Calculate and emit speed info
            if self.start_time:
                elapsed_time = time.time() - self.start_time
                if elapsed_time > 0:
                    speed_mbps = (self.downloaded_bytes / 1024 / 1024) / elapsed_time
                    remaining_items = self.total_count - self.success_count
                    if self.success_count > 0:
                        avg_time_per_item = elapsed_time / self.success_count
                        eta_seconds = remaining_items * avg_time_per_item
                        eta_str = f"{int(eta_seconds//60)}:{int(eta_seconds%60):02d}"
                    else:
                        eta_str = "计算中..."

                    speed_info = f"速度: {speed_mbps:.1f}MB/s | 剩余: {remaining_items} | 预计: {eta_str}"
                    self.speed_update.emit(speed_info)

        return success, index, url
              
    def get_image_urls(self):
        response = self.safe_request(self.url)
        if not response:
            return []
 
        soup = BeautifulSoup(response.text, "html.parser")
        img_tags = soup.select("div.gallerypic img")
        if not img_tags:
            return []
 
        first_img_url = img_tags[0].get("src", "")
         
        # 提取扩展名（如 .jpg 或 .png）
        ext_match = re.search(r'\.(jpg|png|jpeg|gif|webp)$', first_img_url, re.IGNORECASE)
        extension = ext_match.group(0) if ext_match else ".jpg"  # 默认.jpg
 
        is_numbered = re.search(r"/(\d{3})\.[a-zA-Z]+$", first_img_url)
        image_urls = []
         
        # 图片序号
        img_index_match = re.search(r'/(\d+)\.(jpg|png|jpeg|gif|webp)$', first_img_url)
        if img_index_match:
            img_index = img_index_match.group(1)
             
        parsed = urlparse(first_img_url)
        base_path = os.path.dirname(parsed.path)
        base_url = urlunparse((parsed.scheme, parsed.netloc, base_path, '', '', ''))
 
        if is_numbered:
            for i in range(1, self.total_count + 1):
                if int(img_index) > 1:
                    i = i + int(img_index)
                img_url = f"{base_url}/{i:03d}{extension}"
                image_urls.append(img_url)
        else:
            for i in range(0, self.total_count):
                if int(img_index) > 1:
                    i = i + int(img_index)
                img_url = f"{base_url}/{i}{extension}"
                image_urls.append(img_url)
 
        return image_urls
 
  
    def process_album(self):
        """Process album with concurrent downloads"""
        image_urls = self.get_image_urls()
        if not image_urls:
            return False

        album_dir = os.path.join(self.album_title, self.title) if self.album_title else self.title
        os.makedirs(album_dir, exist_ok=True)

        self.total_count = len(image_urls)
        self.success_count = 0
        self.start_time = time.time()  # Record start time
        self.downloaded_bytes = 0

        # Prepare download tasks
        download_tasks = []
        for index, img_url in enumerate(image_urls):
            filename_from_url = os.path.basename(img_url)
            filename = os.path.join(album_dir, filename_from_url)
            download_tasks.append((img_url, filename, index))

        # Use ThreadPoolExecutor for concurrent downloads
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all download tasks
            future_to_task = {
                executor.submit(self.download_single_image, task): task
                for task in download_tasks
            }

            # Process completed downloads
            for future in as_completed(future_to_task):
                try:
                    success, index, url = future.result()
                    if not success:
                        print(f"Failed to download: {url}")
                except Exception as e:
                    print(f"Download error: {e}")

        return self.success_count > 0
  
  
from PySide6.QtCore import QEvent
class _FuncEvent(QEvent):
    def __init__(self, func):
        super().__init__(QEvent.Type.User)
        self.func = func
  
class ThumbnailViewer(QDialog):
    def __init__(self, image_urls, parent=None):
        super().__init__(parent)
        title = "缩略图预览 共" + str(len(image_urls)) + "张"
        self.setWindowTitle(title)
        self.resize(850, 600)
  
        scroll = QScrollArea(self)
        scroll.setWidgetResizable(True)
  
        container = QWidget()
        scroll.setWidget(container)
  
        layout = QVBoxLayout(self)
        layout.addWidget(scroll)
  
        grid = QGridLayout(container)
        self.nam = QNetworkAccessManager(self)
  
        self.thumb_size = QSize(150, 250)  # 缩略图大小
  
        for i, url in enumerate(image_urls):
            # 创建一个垂直容器，放图片和序号label
            widget = QWidget()
            v_layout = QVBoxLayout(widget)
            v_layout.setContentsMargins(0, 0, 0, 0)
            v_layout.setSpacing(2)
  
            label = QLabel("加载中…")
            label.setFixedSize(self.thumb_size)
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("border: 1px solid gray;")
  
            number_label = QLabel(str(i + 1))
            number_label.setAlignment(Qt.AlignCenter)
  
            v_layout.addWidget(label)
            v_layout.addWidget(number_label)
  
            grid.addWidget(widget, i // 5, i % 5)  # 每行5个
  
            self.load_image_async(url, label)
  
    def load_image_async(self, url, label):
        request = QNetworkRequest(QUrl(url))
        request.setAttribute(QNetworkRequest.Http2AllowedAttribute, False)  # 禁用 HTTP/2
        reply = self.nam.get(request)
  
        def handle_finished():
            pixmap = QPixmap()
            if pixmap.loadFromData(reply.readAll()):
                label.setPixmap(pixmap.scaled(
                    self.thumb_size, Qt.KeepAspectRatio, Qt.SmoothTransformation))
            else:
                label.setText("加载失败")
            reply.deleteLater()
  
        reply.finished.connect(handle_finished)


class ResumeDownloadDialog(QDialog):
    def __init__(self, incomplete_sessions, parent=None):
        super().__init__(parent)
        self.setWindowTitle("恢复下载")
        self.setModal(True)
        self.resize(600, 400)
        self.incomplete_sessions = incomplete_sessions
        self.selected_session = None

        layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel("发现未完成的下载任务")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 会话列表
        self.session_tree = QTreeWidget()
        self.session_tree.setHeaderLabels(["会话名称", "创建时间", "总数", "已完成", "待下载", "进度"])
        self.session_tree.setSelectionMode(QTreeWidget.SingleSelection)

        for session_id, session_data in incomplete_sessions.items():
            item = QTreeWidgetItem()
            item.setText(0, session_data.get("session_name", "未知会话"))
            item.setText(1, session_data.get("created_time", "")[:19].replace("T", " "))
            item.setText(2, str(session_data.get("total_items", 0)))
            item.setText(3, str(len(session_data.get("completed_items", []))))
            item.setText(4, str(len(session_data.get("pending_items", []))))

            total = session_data.get("total_items", 0)
            completed = len(session_data.get("completed_items", []))
            progress = int((completed / total * 100)) if total > 0 else 0
            item.setText(5, f"{progress}%")

            item.setData(0, Qt.ItemDataRole.UserRole, session_data)
            self.session_tree.addTopLevelItem(item)

        # 调整列宽
        for i in range(6):
            self.session_tree.resizeColumnToContents(i)

        layout.addWidget(self.session_tree)

        # 详细信息
        self.detail_label = QLabel("请选择一个会话查看详细信息")
        self.detail_label.setStyleSheet("background-color: #f5f5f5; padding: 10px; border-radius: 5px;")
        layout.addWidget(self.detail_label)

        # 连接选择事件
        self.session_tree.itemSelectionChanged.connect(self.on_selection_changed)

        # 按钮
        button_layout = QHBoxLayout()
        self.resume_button = QPushButton("恢复下载")
        self.skip_button = QPushButton("跳过")
        self.delete_button = QPushButton("删除会话")

        self.resume_button.clicked.connect(self.accept)
        self.skip_button.clicked.connect(self.reject)
        self.delete_button.clicked.connect(self.delete_session)

        self.resume_button.setEnabled(False)
        self.delete_button.setEnabled(False)

        button_layout.addWidget(self.delete_button)
        button_layout.addStretch()
        button_layout.addWidget(self.skip_button)
        button_layout.addWidget(self.resume_button)
        layout.addLayout(button_layout)

    def on_selection_changed(self):
        selected_items = self.session_tree.selectedItems()
        if selected_items:
            item = selected_items[0]
            session_data = item.data(0, Qt.ItemDataRole.UserRole)
            self.selected_session = session_data

            # 更新详细信息
            total = session_data.get("total_items", 0)
            completed = len(session_data.get("completed_items", []))
            failed = len(session_data.get("failed_items", []))
            pending = len(session_data.get("pending_items", []))

            detail_text = f"""会话详情：
• 会话名称：{session_data.get("session_name", "未知")}
• 创建时间：{session_data.get("created_time", "")[:19].replace("T", " ")}
• 总计项目：{total} 个
• 已完成：{completed} 个
• 失败：{failed} 个
• 待下载：{pending} 个
• 完成进度：{int((completed / total * 100)) if total > 0 else 0}%"""

            self.detail_label.setText(detail_text)
            self.resume_button.setEnabled(True)
            self.delete_button.setEnabled(True)
        else:
            self.selected_session = None
            self.detail_label.setText("请选择一个会话查看详细信息")
            self.resume_button.setEnabled(False)
            self.delete_button.setEnabled(False)

    def delete_session(self):
        if not self.selected_session:
            return

        reply = QMessageBox.question(self, "确认删除",
                                   "确定要删除这个下载会话吗？\n这将清除所有相关的进度记录。",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            # 从进度文件中删除会话
            progress_manager = DownloadProgressManager()
            all_sessions = progress_manager.load_all_sessions()

            # 找到并删除对应的会话
            session_to_delete = None
            for session_id, session_data in all_sessions.items():
                if session_data == self.selected_session:
                    session_to_delete = session_id
                    break

            if session_to_delete:
                del all_sessions[session_to_delete]
                # 保存更新后的数据
                with open(progress_manager.progress_file, 'w', encoding='utf-8') as f:
                    json.dump(all_sessions, f, ensure_ascii=False, indent=2)

            # 从UI中移除
            selected_items = self.session_tree.selectedItems()
            if selected_items:
                index = self.session_tree.indexOfTopLevelItem(selected_items[0])
                self.session_tree.takeTopLevelItem(index)

            self.selected_session = None
            self.detail_label.setText("会话已删除")
            self.resume_button.setEnabled(False)
            self.delete_button.setEnabled(False)

    def get_selected_session(self):
        return self.selected_session


class DownloadSettingsDialog(QDialog):
    def __init__(self, max_workers, timeout, parent=None):
        super().__init__(parent)
        self.setWindowTitle("下载设置")
        self.setModal(True)
        self.resize(300, 200)

        layout = QVBoxLayout(self)
        form_layout = QFormLayout()

        # 并发线程数设置
        self.workers_spinbox = QSpinBox()
        self.workers_spinbox.setRange(1, 16)
        self.workers_spinbox.setValue(max_workers)
        self.workers_spinbox.setToolTip("同时下载的线程数，建议1-8个")
        form_layout.addRow("并发线程数:", self.workers_spinbox)

        # 超时时间设置
        self.timeout_spinbox = QSpinBox()
        self.timeout_spinbox.setRange(10, 120)
        self.timeout_spinbox.setValue(timeout)
        self.timeout_spinbox.setSuffix(" 秒")
        self.timeout_spinbox.setToolTip("单个文件下载超时时间")
        form_layout.addRow("下载超时:", self.timeout_spinbox)

        layout.addLayout(form_layout)

        # 说明文字
        info_label = QLabel("提示：\n• 线程数越多下载越快，但会占用更多网络带宽\n• 网络较慢时建议减少线程数\n• 超时时间过短可能导致大文件下载失败")
        info_label.setStyleSheet("color: #666; font-size: 12px;")
        layout.addWidget(info_label)

        # 按钮
        button_layout = QHBoxLayout()
        ok_button = QPushButton("确定")
        cancel_button = QPushButton("取消")

        ok_button.clicked.connect(self.accept)
        cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)

    def get_settings(self):
        return self.workers_spinbox.value(), self.timeout_spinbox.value()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = GalleryCrawler()
    window.show()
    sys.exit(app.exec())