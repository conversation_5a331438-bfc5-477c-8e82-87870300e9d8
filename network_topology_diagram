// 网络拓扑图
digraph {
	node [fontname=SimSun shape=box]
	edge [fontname=SimSun]
	switch [label="交换机/集线器" fontsize=12 shape=box]
	db_server [label="数据库服务器" fontsize=10]
	web_server [label="Web服务器" fontsize=10]
	print_server [label="打印服务器" fontsize=10]
	printer [label="打印机" shape=note]
	client1 [label="客户机" fontsize=10]
	client2 [label="客户机" fontsize=10]
	switch -> db_server
	switch -> web_server
	switch -> print_server
	switch -> client1
	switch -> client2
	print_server -> printer
	switch [pos="0,0!"]
	db_server [pos="-2,-2!"]
	web_server [pos="-0.5,-2!"]
	print_server [pos="2,0.5!"]
	printer [pos="2.8,0.5!"]
	client1 [pos="1,-2!"]
	client2 [pos="2.5,-2!"]
}
