import pygame
import random
import time
import json
import os

# 初始化pygame
pygame.init()

# 颜色定义
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
GRAY = (128, 128, 128)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
CYAN = (0, 255, 255)
MAGENTA = (255, 0, 255)
VIOLET = (238, 130, 238)
ORANGE = (255, 165, 0)

# 新增UI颜色
BACKGROUND_COLOR = (40, 44, 52)  # 深色背景
PANEL_COLOR = (50, 54, 62)       # 面板颜色
BORDER_COLOR = (60, 64, 72)      # 边框颜色
TEXT_COLOR = (220, 220, 220)     # 文字颜色
HIGHLIGHT_COLOR = (100, 200, 255) # 高亮颜色

# 方块颜色（使用更柔和的色调）
COLORS = [
    (0, 240, 240),    # I - 青色
    (0, 0, 240),      # J - 蓝色
    (240, 160, 0),    # L - 橙色
    (255, 192, 203),  # O - 粉色（原为黄色）
    (0, 240, 0),      # S - 绿色
    (160, 0, 240),    # T - 紫色
    (240, 0, 0)       # Z - 红色
]

# 方块形状定义
SHAPES = [
    [[1, 1, 1, 1]],                         # I
    [[1, 0, 0], [1, 1, 1]],                 # J
    [[0, 0, 1], [1, 1, 1]],                 # L
    [[1, 1], [1, 1]],                       # O
    [[0, 1, 1], [1, 1, 0]],                 # S
    [[0, 1, 0], [1, 1, 1]],                 # T
    [[1, 1, 0], [0, 1, 1]]                  # Z
]

# 游戏设置
BLOCK_SIZE = 30
GRID_WIDTH = 12  # 增加游戏区域宽度，从10增加到12
GRID_HEIGHT = 20
SCREEN_WIDTH = BLOCK_SIZE * (GRID_WIDTH + 5)  # 再减小右侧区域，从+6改为+5
SCREEN_HEIGHT = BLOCK_SIZE * (GRID_HEIGHT + 2)  # 增加窗口高度，添加额外空间
GAME_AREA_LEFT = BLOCK_SIZE * 0.5  # 保持较小的左边距
GAME_AREA_TOP = BLOCK_SIZE * 1  # 保持顶部间距
NEXT_PIECE_LEFT = BLOCK_SIZE * (GRID_WIDTH + 1.25)  # 将信息面板向右移动，从0.75改为1.25
NEXT_PIECE_TOP = BLOCK_SIZE * 3  # 相应调整下一个方块显示位置
SCORE_LEFT = NEXT_PIECE_LEFT
SCORE_TOP = BLOCK_SIZE * 9  # 相应调整分数显示位置
LEVEL_LEFT = NEXT_PIECE_LEFT
LEVEL_TOP = BLOCK_SIZE * 11  # 相应调整等级显示位置
LINES_LEFT = NEXT_PIECE_LEFT
LINES_TOP = BLOCK_SIZE * 13  # 相应调整消除行数显示位置

# 游戏状态
STATE_MENU = 0      # 主菜单
STATE_MODE_SELECT = 1  # 模式选择
STATE_PLAYING = 2   # 游戏中
STATE_PAUSED = 3    # 暂停
STATE_SETTINGS = 4  # 设置

# 游戏模式
MODE_CLASSIC = 0    # 经典模式
MODE_SPEED = 1      # 极速模式
MODE_PUZZLE = 2     # 解谜模式

# 存档文件路径
SAVE_FILE = "tetris_save.json"
HIGH_SCORE_FILE = "tetris_high_score.json"

# 游戏速度设置
BASE_SPEED = 1.0  # 基础下落速度（秒/格）
SPEED_INCREASE = 0.05  # 每级增加的速度

# 默认设置
DEFAULT_SETTINGS = {
    'show_ghost_piece': True,  # 是否显示底部投影
    'base_speed': BASE_SPEED,  # 基础下落速度
    'colors': COLORS.copy()  # 方块颜色
}

# UI设置
PANEL_PADDING = 15
PANEL_BORDER_RADIUS = 10
BUTTON_HEIGHT = 45
BUTTON_BORDER_RADIUS = 8
TITLE_MARGIN = 40

class Button:
    def __init__(self, x, y, width, height, text, font, color=TEXT_COLOR, hover_color=HIGHLIGHT_COLOR):
        self.rect = pygame.Rect(x, y, width, height)
        self.text = text
        self.font = font
        self.color = color
        self.hover_color = hover_color
        self.is_hovered = False
        self.animation_progress = 0  # 用于动画效果
        self.animation_speed = 0.2   # 动画速度
        
    def draw(self, screen):
        # 绘制按钮背景
        if self.is_hovered:
            self.animation_progress = min(1.0, self.animation_progress + self.animation_speed)
        else:
            self.animation_progress = max(0.0, self.animation_progress - self.animation_speed)
            
        # 计算当前颜色
        current_color = tuple(int(self.color[i] + (self.hover_color[i] - self.color[i]) * self.animation_progress) 
                            for i in range(3))
        
        # 绘制圆角矩形背景
        pygame.draw.rect(screen, PANEL_COLOR, self.rect, border_radius=BUTTON_BORDER_RADIUS)
        
        # 绘制边框
        border_width = 2
        pygame.draw.rect(screen, current_color, self.rect, border_width, border_radius=BUTTON_BORDER_RADIUS)
        
        # 绘制按钮文本
        text_surface = self.font.render(self.text, True, current_color)
        text_rect = text_surface.get_rect(center=self.rect.center)
        screen.blit(text_surface, text_rect)
        
    def check_hover(self, pos):
        self.is_hovered = self.rect.collidepoint(pos)
        return self.is_hovered
        
    def is_clicked(self, pos, event):
        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
            return self.rect.collidepoint(pos)
        return False

class Tetris:
    def __init__(self):
        # 设置窗口位置（居中显示）
        os.environ['SDL_VIDEO_CENTERED'] = '1'
        
        # 创建游戏窗口
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("俄罗斯方块")
        
        # 设置窗口图标
        try:
            icon = pygame.Surface((32, 32))
            icon.fill(PANEL_COLOR)
            # 使用田字形（O型方块）的颜色作为图标
            o_block_color = COLORS[3]  # O型方块的颜色
            for i in range(2):
                for j in range(2):
                    pygame.draw.rect(icon, o_block_color, (i*16, j*16, 16, 16))
                    pygame.draw.rect(icon, (255, 255, 255), (i*16, j*16, 16, 16), 1)
            pygame.display.set_icon(icon)
        except:
            pass  # 如果设置图标失败，继续运行游戏
            
        # 加载最高分
        self.high_score = self.load_high_score()
            
        self.clock = pygame.time.Clock()
        # 使用支持中文的字体
        try:
            self.font = pygame.font.SysFont('Microsoft YaHei', 22)  # 调小字体大小
            self.big_font = pygame.font.SysFont('Microsoft YaHei', 40)
            self.small_font = pygame.font.SysFont('Microsoft YaHei', 16)  # 调小字体大小
        except:
            # 如果微软雅黑不可用，尝试使用其他可能支持中文的字体
            self.font = pygame.font.SysFont('SimHei', 22)  # 调小字体大小
            self.big_font = pygame.font.SysFont('SimHei', 40)
            self.small_font = pygame.font.SysFont('SimHei', 16)  # 调小字体大小
        # 按键重复设置
        self.key_repeat_delay = 200  # 初始延迟（毫秒）
        self.key_repeat_interval = 50  # 重复间隔（毫秒）
        self.fast_repeat_threshold = 1000  # 启用快速重复的阈值（毫秒）
        self.last_key_time = {'left': 0, 'right': 0, 'down': 0}
        self.key_press_start = {'left': 0, 'right': 0, 'down': 0}  # 记录按键按下的起始时间
        
        # 游戏状态和模式
        self.state = STATE_MENU
        self.game_mode = MODE_CLASSIC
        
        # 游戏设置
        self.settings = DEFAULT_SETTINGS.copy()
        self.selected_color_index = 0  # 当前选中的颜色索引
        self.color_names = ["I型方块", "J型方块", "L型方块", "O型方块", "S型方块", "T型方块", "Z型方块"]
        self.speed_slider_pos = 0.5  # 速度滑块位置 (0.0-1.0)
        
        # 创建按钮
        self.create_buttons()
        
        # 游戏结束时的按钮
        self.game_over_buttons = []
        self.create_game_over_buttons()
        
        # 初始化游戏
        self.reset_game()
        
    def create_buttons(self):
        # 主菜单按钮
        button_width = 200
        button_height = 50
        button_x = SCREEN_WIDTH // 2 - button_width // 2
        button_y_start = SCREEN_HEIGHT // 2 - 100
        button_spacing = 70
        
        self.menu_buttons = [
            Button(button_x, button_y_start, button_width, button_height, "开始游戏", self.font),
            Button(button_x, button_y_start + button_spacing, button_width, button_height, "继续游戏", self.font),
            Button(button_x, button_y_start + button_spacing * 2, button_width, button_height, "游戏模式", self.font),
            Button(button_x, button_y_start + button_spacing * 3, button_width, button_height, "设置", self.font)
        ]
        
        # 模式选择按钮
        self.mode_buttons = [
            Button(button_x, button_y_start, button_width, button_height, "经典模式", self.font),
            Button(button_x, button_y_start + button_spacing, button_width, button_height, "极速模式", self.font),
            Button(button_x, button_y_start + button_spacing * 2, button_width, button_height, "解谜模式", self.font),
            Button(button_x, button_y_start + button_spacing * 3, button_width, button_height, "返回", self.font)
        ]
        
        # 暂停菜单按钮
        self.pause_buttons = [
            Button(button_x, button_y_start, button_width, button_height, "继续游戏", self.font),
            Button(button_x, button_y_start + button_spacing, button_width, button_height, "保存游戏", self.font),
            Button(button_x, button_y_start + button_spacing * 2, button_width, button_height, "返回主菜单", self.font)
        ]
        
        # 设置界面按钮 - 更新位置
        small_button_width = 30
        small_button_height = 30
        
        # 左右箭头位置
        left_arrow_x = SCREEN_WIDTH // 2 - 85
        right_arrow_x = SCREEN_WIDTH // 2 + 55
        arrow_y = SCREEN_HEIGHT // 2 - 65
        
        # 颜色调整按钮位置
        color_button_x = SCREEN_WIDTH // 2 + 30
        color_button_y_start = SCREEN_HEIGHT // 2
        
        # 创建按钮
        self.settings_buttons = [
            # 颜色选择器左右按钮
            Button(left_arrow_x, arrow_y, small_button_width, small_button_height, "<", self.font),
            Button(right_arrow_x, arrow_y, small_button_width, small_button_height, ">", self.font),
            
            # 颜色调整按钮 (R+, R-, G+, G-, B+, B-)
            Button(color_button_x, color_button_y_start, small_button_width, small_button_height, "R+", self.small_font),
            Button(color_button_x + 40, color_button_y_start, small_button_width, small_button_height, "R-", self.small_font),
            Button(color_button_x, color_button_y_start + 25, small_button_width, small_button_height, "G+", self.small_font),
            Button(color_button_x + 40, color_button_y_start + 25, small_button_width, small_button_height, "G-", self.small_font),
            Button(color_button_x, color_button_y_start + 50, small_button_width, small_button_height, "B+", self.small_font),
            Button(color_button_x + 40, color_button_y_start + 50, small_button_width, small_button_height, "B-", self.small_font),
            
            # 底部投影开关按钮
            Button(button_x, button_y_start + button_spacing * 3, button_width, button_height, "底部投影: 开" if self.settings['show_ghost_piece'] else "底部投影: 关", self.font),
            # 返回按钮
            Button(button_x, button_y_start + button_spacing * 4, button_width, button_height, "返回", self.font)
        ]
    
    def create_game_over_buttons(self):
        button_width = 200
        button_height = 50
        button_x = SCREEN_WIDTH // 2 - button_width // 2
        
        # 创建"再来一局"按钮
        replay_button = Button(button_x, 0, button_width, button_height, "再来一局", self.font)
        # Y坐标在draw_game_over方法中动态设置
        
        self.game_over_buttons = [replay_button]
    
    def reset_game(self):
        self.grid = [[0 for _ in range(GRID_WIDTH)] for _ in range(GRID_HEIGHT)]
        self.current_piece = self.new_piece()
        self.next_piece = self.new_piece()
        self.game_over = False
        self.score = 0
        self.level = 1
        self.lines_cleared = 0
        self.last_drop_time = time.time()
        self.drop_speed = self.settings['base_speed']  # 使用设置中的基础速度
        # 不重置最高分，保留历史记录
        
        # 根据游戏模式调整参数
        if self.game_mode == MODE_SPEED:
            self.drop_speed = self.settings['base_speed'] * 0.5  # 极速模式下速度更快
        elif self.game_mode == MODE_PUZZLE:
            # 解谜模式可以添加特殊规则
            pass
        
    def new_piece(self):
        shape_idx = random.randint(0, len(SHAPES) - 1)
        return {
            'shape': SHAPES[shape_idx],
            'color': self.settings['colors'][shape_idx],  # 使用设置中的自定义颜色
            'x': GRID_WIDTH // 2 - len(SHAPES[shape_idx][0]) // 2,
            'y': 0
        }
        
    def valid_position(self, piece, x_offset=0, y_offset=0):
        for y, row in enumerate(piece['shape']):
            for x, cell in enumerate(row):
                if cell:
                    try:
                        if (piece['y'] + y + y_offset < 0 or
                            piece['x'] + x + x_offset < 0 or
                            piece['x'] + x + x_offset >= GRID_WIDTH or
                            piece['y'] + y + y_offset >= GRID_HEIGHT or
                            self.grid[piece['y'] + y + y_offset][piece['x'] + x + x_offset]):
                            return False
                    except IndexError:
                        return False
        return True
        
    def rotate_piece(self, piece):
        # 转置矩阵并反转每一行以实现90度旋转
        rotated_shape = [list(row) for row in zip(*piece['shape'])][::-1]
        rotated_piece = {
            'shape': rotated_shape,
            'color': piece['color'],
            'x': piece['x'],
            'y': piece['y']
        }
        
        # 如果旋转后的位置无效，尝试调整位置
        if not self.valid_position(rotated_piece):
            # 尝试向左移动
            rotated_piece['x'] -= 1
            if self.valid_position(rotated_piece):
                return rotated_piece
                
            # 恢复并尝试向右移动
            rotated_piece['x'] += 2
            if self.valid_position(rotated_piece):
                return rotated_piece
                
            # 恢复并尝试向上移动
            rotated_piece['x'] -= 1
            rotated_piece['y'] -= 1
            if self.valid_position(rotated_piece):
                return rotated_piece
                
            # 如果所有调整都失败，返回原始方块
            return piece
        return rotated_piece
        
    def merge_piece(self):
        for y, row in enumerate(self.current_piece['shape']):
            for x, cell in enumerate(row):
                if cell:
                    self.grid[self.current_piece['y'] + y][self.current_piece['x'] + x] = self.current_piece['color']
        
    def clear_lines(self):
        lines_to_clear = []
        for y in range(GRID_HEIGHT):
            if all(self.grid[y]):
                lines_to_clear.append(y)
                
        for y in lines_to_clear:
            del self.grid[y]
            self.grid.insert(0, [0 for _ in range(GRID_WIDTH)])
            
        # 计分系统
        if lines_to_clear:
            self.lines_cleared += len(lines_to_clear)
            if len(lines_to_clear) == 1:
                self.score += 100 * self.level
            elif len(lines_to_clear) == 2:
                self.score += 300 * self.level
            elif len(lines_to_clear) == 3:
                self.score += 500 * self.level
            elif len(lines_to_clear) == 4:
                self.score += 800 * self.level
                
            # 每清除10行升一级
            self.level = self.lines_cleared // 10 + 1
            self.drop_speed = max(0.05, BASE_SPEED - (self.level - 1) * SPEED_INCREASE)
                
    def draw_grid(self):
        # 绘制游戏区域背景
        pygame.draw.rect(self.screen, PANEL_COLOR, (GAME_AREA_LEFT, GAME_AREA_TOP, 
                                                   GRID_WIDTH * BLOCK_SIZE, 
                                                   GRID_HEIGHT * BLOCK_SIZE),
                        border_radius=PANEL_BORDER_RADIUS)
        
        # 绘制网格线（使用更柔和的颜色）
        for x in range(GRID_WIDTH + 1):
            pygame.draw.line(self.screen, BORDER_COLOR, 
                            (GAME_AREA_LEFT + x * BLOCK_SIZE, GAME_AREA_TOP),
                            (GAME_AREA_LEFT + x * BLOCK_SIZE, GAME_AREA_TOP + GRID_HEIGHT * BLOCK_SIZE))
        for y in range(GRID_HEIGHT + 1):
            pygame.draw.line(self.screen, BORDER_COLOR, 
                            (GAME_AREA_LEFT, GAME_AREA_TOP + y * BLOCK_SIZE),
                            (GAME_AREA_LEFT + GRID_WIDTH * BLOCK_SIZE, GAME_AREA_TOP + y * BLOCK_SIZE))
        
        # 绘制已落下的方块
        for y in range(GRID_HEIGHT):
            for x in range(GRID_WIDTH):
                if self.grid[y][x]:
                    # 绘制方块主体
                    pygame.draw.rect(self.screen, self.grid[y][x], 
                                    (GAME_AREA_LEFT + x * BLOCK_SIZE, 
                                     GAME_AREA_TOP + y * BLOCK_SIZE, 
                                     BLOCK_SIZE, BLOCK_SIZE))
                    # 绘制方块高光
                    pygame.draw.line(self.screen, (255, 255, 255, 100),
                                    (GAME_AREA_LEFT + x * BLOCK_SIZE, GAME_AREA_TOP + y * BLOCK_SIZE),
                                    (GAME_AREA_LEFT + (x + 1) * BLOCK_SIZE, GAME_AREA_TOP + y * BLOCK_SIZE))
                    pygame.draw.line(self.screen, (255, 255, 255, 100),
                                    (GAME_AREA_LEFT + x * BLOCK_SIZE, GAME_AREA_TOP + y * BLOCK_SIZE),
                                    (GAME_AREA_LEFT + x * BLOCK_SIZE, GAME_AREA_TOP + (y + 1) * BLOCK_SIZE))
                    # 绘制方块阴影
                    pygame.draw.line(self.screen, (0, 0, 0, 100),
                                    (GAME_AREA_LEFT + (x + 1) * BLOCK_SIZE, GAME_AREA_TOP + y * BLOCK_SIZE),
                                    (GAME_AREA_LEFT + (x + 1) * BLOCK_SIZE, GAME_AREA_TOP + (y + 1) * BLOCK_SIZE))
                    pygame.draw.line(self.screen, (0, 0, 0, 100),
                                    (GAME_AREA_LEFT + x * BLOCK_SIZE, GAME_AREA_TOP + (y + 1) * BLOCK_SIZE),
                                    (GAME_AREA_LEFT + (x + 1) * BLOCK_SIZE, GAME_AREA_TOP + (y + 1) * BLOCK_SIZE))
                    
    def draw_ghost_piece(self):
        if self.current_piece and self.settings['show_ghost_piece']:
            # 创建一个投影方块
            ghost_piece = {
                'shape': self.current_piece['shape'],
                'color': self.current_piece['color'],
                'x': self.current_piece['x'],
                'y': self.current_piece['y']
            }
            
            # 将投影方块移动到底部
            while self.valid_position(ghost_piece, y_offset=1):
                ghost_piece['y'] += 1
            
            # 绘制半透明的投影方块
            for y, row in enumerate(ghost_piece['shape']):
                for x, cell in enumerate(row):
                    if cell:
                        # 计算方块位置
                        block_x = GAME_AREA_LEFT + (ghost_piece['x'] + x) * BLOCK_SIZE
                        block_y = GAME_AREA_TOP + (ghost_piece['y'] + y) * BLOCK_SIZE
                        
                        # 创建半透明颜色
                        r, g, b = ghost_piece['color']
                        
                        # 创建一个表面来绘制半透明方块
                        s = pygame.Surface((BLOCK_SIZE, BLOCK_SIZE), pygame.SRCALPHA)
                        s.fill((r, g, b, 50))  # 降低透明度使其更加柔和
                        self.screen.blit(s, (block_x, block_y))
                        
                        # 绘制细线边框
                        border_color = (255, 255, 255, 80)
                        pygame.draw.rect(self.screen, border_color, 
                                        (block_x, block_y, BLOCK_SIZE, BLOCK_SIZE), 1)
    
    def draw_current_piece(self):
        if self.current_piece:
            # 绘制当前方块
            for y, row in enumerate(self.current_piece['shape']):
                for x, cell in enumerate(row):
                    if cell:
                        # 计算方块位置
                        block_x = GAME_AREA_LEFT + (self.current_piece['x'] + x) * BLOCK_SIZE
                        block_y = GAME_AREA_TOP + (self.current_piece['y'] + y) * BLOCK_SIZE
                        
                        # 绘制方块主体
                        pygame.draw.rect(self.screen, self.current_piece['color'], 
                                        (block_x, block_y, BLOCK_SIZE, BLOCK_SIZE))
                        
                        # 绘制方块高光
                        pygame.draw.line(self.screen, (255, 255, 255, 100),
                                        (block_x, block_y),
                                        (block_x + BLOCK_SIZE, block_y))
                        pygame.draw.line(self.screen, (255, 255, 255, 100),
                                        (block_x, block_y),
                                        (block_x, block_y + BLOCK_SIZE))
                        
                        # 绘制方块阴影
                        pygame.draw.line(self.screen, (0, 0, 0, 100),
                                        (block_x + BLOCK_SIZE, block_y),
                                        (block_x + BLOCK_SIZE, block_y + BLOCK_SIZE))
                        pygame.draw.line(self.screen, (0, 0, 0, 100),
                                        (block_x, block_y + BLOCK_SIZE),
                                        (block_x + BLOCK_SIZE, block_y + BLOCK_SIZE))
        
    def draw_next_piece(self):
        # 绘制"下一个"区域背景
        pygame.draw.rect(self.screen, PANEL_COLOR, (NEXT_PIECE_LEFT, NEXT_PIECE_TOP, 
                                                   BLOCK_SIZE * 3.5, BLOCK_SIZE * 4),  # 减小宽度从4改为3.5
                        border_radius=PANEL_BORDER_RADIUS)
        
        # 绘制"下一个"标题
        next_text = self.font.render("下一个:", True, TEXT_COLOR)
        self.screen.blit(next_text, (NEXT_PIECE_LEFT, NEXT_PIECE_TOP - 30))
        
        # 绘制下一个方块
        if self.next_piece:
            for y, row in enumerate(self.next_piece['shape']):
                for x, cell in enumerate(row):
                    if cell:
                        # 计算方块位置 - 调整为居中显示
                        block_x = NEXT_PIECE_LEFT + (x + (3.5 - len(self.next_piece['shape'][0])) / 2) * BLOCK_SIZE
                        block_y = NEXT_PIECE_TOP + (y + (4 - len(self.next_piece['shape'])) // 2) * BLOCK_SIZE
                        
                        # 绘制方块主体
                        pygame.draw.rect(self.screen, self.next_piece['color'], 
                                        (block_x, block_y, BLOCK_SIZE, BLOCK_SIZE))
                        # 绘制方块高光
                        pygame.draw.line(self.screen, (255, 255, 255, 100),
                                        (block_x, block_y),
                                        (block_x + BLOCK_SIZE, block_y))
                        pygame.draw.line(self.screen, (255, 255, 255, 100),
                                        (block_x, block_y),
                                        (block_x, block_y + BLOCK_SIZE))
                        # 绘制方块阴影
                        pygame.draw.line(self.screen, (0, 0, 0, 100),
                                        (block_x + BLOCK_SIZE, block_y),
                                        (block_x + BLOCK_SIZE, block_y + BLOCK_SIZE))
                        pygame.draw.line(self.screen, (0, 0, 0, 100),
                                        (block_x, block_y + BLOCK_SIZE),
                                        (block_x + BLOCK_SIZE, block_y + BLOCK_SIZE))
        
    def draw_info(self):
        # 创建信息面板
        info_panel = pygame.Rect(SCORE_LEFT - PANEL_PADDING, SCORE_TOP - PANEL_PADDING,
                               BLOCK_SIZE * 4.5 + PANEL_PADDING * 2,  # 增加宽度以完整显示最高分
                               BLOCK_SIZE * 7 + PANEL_PADDING * 2)  # 增加高度以容纳最高分
        pygame.draw.rect(self.screen, PANEL_COLOR, info_panel, border_radius=PANEL_BORDER_RADIUS)
        
        # 绘制游戏信息
        score_text = self.font.render(f"分数: {self.score}", True, TEXT_COLOR)
        high_score_text = self.font.render(f"最高分: {self.high_score}", True, TEXT_COLOR)
        level_text = self.font.render(f"等级: {self.level}", True, TEXT_COLOR)
        lines_text = self.font.render(f"已消除: {self.lines_cleared}", True, TEXT_COLOR)
        
        self.screen.blit(score_text, (SCORE_LEFT, SCORE_TOP))
        self.screen.blit(high_score_text, (SCORE_LEFT, SCORE_TOP + BLOCK_SIZE * 1.5))
        self.screen.blit(level_text, (LEVEL_LEFT, LEVEL_TOP + BLOCK_SIZE * 1.5))
        self.screen.blit(lines_text, (LINES_LEFT, LINES_TOP + BLOCK_SIZE * 1.5))
        
    def draw_game_over(self):
        # 创建半透明遮罩
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 180))
        self.screen.blit(overlay, (0, 0))
        
        # 创建游戏结束面板
        panel_width = 400
        panel_height = 250  # 增加高度以容纳按钮
        panel_x = SCREEN_WIDTH // 2 - panel_width // 2
        panel_y = SCREEN_HEIGHT // 2 - panel_height // 2
        
        # 绘制面板背景
        pygame.draw.rect(self.screen, PANEL_COLOR, 
                        (panel_x, panel_y, panel_width, panel_height),
                        border_radius=PANEL_BORDER_RADIUS)
        
        # 绘制游戏结束文本
        game_over_text = self.big_font.render("游戏结束", True, TEXT_COLOR)
        game_over_shadow = self.big_font.render("游戏结束", True, (0, 0, 0, 100))
        self.screen.blit(game_over_shadow, (SCREEN_WIDTH // 2 - game_over_text.get_width() // 2 + 2, 
                                          panel_y + 40 + 2))
        self.screen.blit(game_over_text, (SCREEN_WIDTH // 2 - game_over_text.get_width() // 2, 
                                        panel_y + 40))
        
        # 绘制最终分数和最高分
        score_text = self.font.render(f"最终分数: {self.score}", True, TEXT_COLOR)
        self.screen.blit(score_text, (SCREEN_WIDTH // 2 - score_text.get_width() // 2, 
                                    panel_y + 90))
                                    
        high_score_text = self.font.render(f"最高分: {self.high_score}", True, TEXT_COLOR)
        self.screen.blit(high_score_text, (SCREEN_WIDTH // 2 - high_score_text.get_width() // 2, 
                                        panel_y + 120))
        
        # 设置再来一局按钮位置
        self.game_over_buttons[0].rect.y = panel_y + 160
        
        # 绘制再来一局按钮
        for button in self.game_over_buttons:
            button.draw(self.screen)
        
    def draw_pause(self):
        # 创建半透明遮罩
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 180))
        self.screen.blit(overlay, (0, 0))
        
        # 创建暂停面板
        panel_width = 400
        panel_height = 300
        panel_x = SCREEN_WIDTH // 2 - panel_width // 2
        panel_y = SCREEN_HEIGHT // 2 - panel_height // 2
        
        # 绘制面板背景
        pygame.draw.rect(self.screen, PANEL_COLOR, 
                        (panel_x, panel_y, panel_width, panel_height),
                        border_radius=PANEL_BORDER_RADIUS)
        
        # 绘制暂停文本
        pause_text = self.big_font.render("游戏暂停", True, TEXT_COLOR)
        pause_shadow = self.big_font.render("游戏暂停", True, (0, 0, 0, 100))
        self.screen.blit(pause_shadow, (SCREEN_WIDTH // 2 - pause_text.get_width() // 2 + 2, 
                                      panel_y + 40 + 2))
        self.screen.blit(pause_text, (SCREEN_WIDTH // 2 - pause_text.get_width() // 2, 
                                    panel_y + 40))
        
        # 调整暂停菜单按钮位置
        button_width = 200
        button_height = 45
        button_x = SCREEN_WIDTH // 2 - button_width // 2
        button_y_start = panel_y + 100
        button_spacing = 60
        
        for i, button in enumerate(self.pause_buttons):
            button.rect.x = button_x
            button.rect.y = button_y_start + i * button_spacing
            button.rect.width = button_width
            button.rect.height = button_height
            button.draw(self.screen)
        
    def draw_menu(self):
        # 绘制背景
        self.screen.fill(BACKGROUND_COLOR)
        
        # 绘制标题
        title_text = self.big_font.render("俄罗斯方块", True, TEXT_COLOR)
        title_shadow = self.big_font.render("俄罗斯方块", True, (0, 0, 0, 100))
        self.screen.blit(title_shadow, (SCREEN_WIDTH // 2 - title_text.get_width() // 2 + 2, 
                                      SCREEN_HEIGHT // 4 + 2))
        self.screen.blit(title_text, (SCREEN_WIDTH // 2 - title_text.get_width() // 2, 
                                    SCREEN_HEIGHT // 4))
        
        # 绘制按钮
        for button in self.menu_buttons:
            button.draw(self.screen)
            
        pygame.display.flip()
    
    def draw_mode_select(self):
        # 绘制背景
        self.screen.fill(BACKGROUND_COLOR)
        
        # 绘制标题
        title_text = self.big_font.render("选择游戏模式", True, TEXT_COLOR)
        title_shadow = self.big_font.render("选择游戏模式", True, (0, 0, 0, 100))
        self.screen.blit(title_shadow, (SCREEN_WIDTH // 2 - title_text.get_width() // 2 + 2, 
                                      SCREEN_HEIGHT // 4 + 2))
        self.screen.blit(title_text, (SCREEN_WIDTH // 2 - title_text.get_width() // 2, 
                                    SCREEN_HEIGHT // 4))
        
        # 绘制按钮
        for button in self.mode_buttons:
            button.draw(self.screen)
            
        pygame.display.flip()
    
    def draw_settings(self):
        # 绘制背景
        self.screen.fill(BACKGROUND_COLOR)
        
        # 绘制标题
        title_text = self.big_font.render("游戏设置", True, TEXT_COLOR)
        title_shadow = self.big_font.render("游戏设置", True, (0, 0, 0, 100))
        self.screen.blit(title_shadow, (SCREEN_WIDTH // 2 - title_text.get_width() // 2 + 2, 
                                      SCREEN_HEIGHT // 4 - 50 + 2))
        self.screen.blit(title_text, (SCREEN_WIDTH // 2 - title_text.get_width() // 2, 
                                    SCREEN_HEIGHT // 4 - 50))
        
        # 绘制颜色设置面板
        color_panel = pygame.Rect(SCREEN_WIDTH // 2 - 150, SCREEN_HEIGHT // 2 - 150,
                                300, 300)
        pygame.draw.rect(self.screen, PANEL_COLOR, color_panel, border_radius=PANEL_BORDER_RADIUS)
        
        # 绘制颜色选择器标题
        color_selector_title = self.font.render("方块颜色设置", True, TEXT_COLOR)
        self.screen.blit(color_selector_title, (SCREEN_WIDTH // 2 - color_selector_title.get_width() // 2, 
                                              SCREEN_HEIGHT // 2 - 140))
        
        # 计算左右箭头的位置
        small_button_width = 30
        left_arrow_x = SCREEN_WIDTH // 2 - 85  # 左箭头位置
        right_arrow_x = SCREEN_WIDTH // 2 + 55  # 右箭头位置
        arrow_y = SCREEN_HEIGHT // 2 - 65
        
        # 更新左右箭头按钮位置
        self.settings_buttons[0].rect.x = left_arrow_x
        self.settings_buttons[0].rect.y = arrow_y
        self.settings_buttons[1].rect.x = right_arrow_x
        self.settings_buttons[1].rect.y = arrow_y
        
        # 绘制当前选中的方块类型
        block_name = self.font.render(self.color_names[self.selected_color_index], True, TEXT_COLOR)
        block_name_x = SCREEN_WIDTH // 2 - block_name.get_width() // 2
        block_name_y = SCREEN_HEIGHT // 2 - 100
        self.screen.blit(block_name, (block_name_x, block_name_y))
        
        # 绘制颜色预览 - 放在左右箭头中间
        color = self.settings['colors'][self.selected_color_index]
        preview_size = 50
        preview_x = SCREEN_WIDTH // 2 - preview_size // 2  # 居中放置
        preview_y = arrow_y - 10  # 与箭头对齐并稍微上移
        
        # 绘制预览背景
        pygame.draw.rect(self.screen, BORDER_COLOR, 
                        (preview_x - 2, preview_y - 2, preview_size + 4, preview_size + 4),
                        border_radius=8)
        pygame.draw.rect(self.screen, color, 
                        (preview_x, preview_y, preview_size, preview_size),
                        border_radius=6)
        
        # 绘制RGB值
        r, g, b = color
        r_text = self.small_font.render(f"R: {r}", True, (255, 100, 100))
        g_text = self.small_font.render(f"G: {g}", True, (100, 255, 100))
        b_text = self.small_font.render(f"B: {b}", True, (100, 100, 255))
        
        # 调整RGB值的位置
        rgb_x = SCREEN_WIDTH // 2 - 60
        self.screen.blit(r_text, (rgb_x, SCREEN_HEIGHT // 2))
        self.screen.blit(g_text, (rgb_x, SCREEN_HEIGHT // 2 + 25))
        self.screen.blit(b_text, (rgb_x, SCREEN_HEIGHT // 2 + 50))
        
        # 调整颜色调整按钮的位置
        color_button_x = SCREEN_WIDTH // 2 + 30
        color_button_y_start = SCREEN_HEIGHT // 2
        
        # 更新颜色调整按钮位置
        for i in range(6):
            row = i // 2
            col = i % 2
            btn_idx = i + 2  # 因为前两个按钮是左右箭头
            self.settings_buttons[btn_idx].rect.x = color_button_x + col * 40
            self.settings_buttons[btn_idx].rect.y = color_button_y_start + row * 25
        
        # 绘制速度设置面板
        speed_panel = pygame.Rect(SCREEN_WIDTH // 2 - 150, SCREEN_HEIGHT // 2 + 80,
                                300, 150)
        pygame.draw.rect(self.screen, PANEL_COLOR, speed_panel, border_radius=PANEL_BORDER_RADIUS)
        
        # 绘制速度滑块标题
        speed_title = self.font.render("下落速度", True, TEXT_COLOR)
        self.screen.blit(speed_title, (SCREEN_WIDTH // 2 - speed_title.get_width() // 2, 
                                     SCREEN_HEIGHT // 2 + 90))
        
        # 绘制速度滑块
        slider_width = 200
        slider_height = 10
        slider_x = SCREEN_WIDTH // 2 - slider_width // 2
        slider_y = SCREEN_HEIGHT // 2 + 130
        
        # 滑块背景
        pygame.draw.rect(self.screen, BORDER_COLOR, 
                        (slider_x, slider_y, slider_width, slider_height),
                        border_radius=5)
        # 滑块进度
        pygame.draw.rect(self.screen, HIGHLIGHT_COLOR, 
                        (slider_x, slider_y, int(slider_width * self.speed_slider_pos), slider_height),
                        border_radius=5)
        # 滑块手柄
        pygame.draw.circle(self.screen, TEXT_COLOR, 
                         (int(slider_x + slider_width * self.speed_slider_pos), slider_y + slider_height // 2), 10)
        pygame.draw.circle(self.screen, BORDER_COLOR, 
                         (int(slider_x + slider_width * self.speed_slider_pos), slider_y + slider_height // 2), 10, 1)
        
        # 显示速度值
        speed_value = 2.0 - self.settings['base_speed']
        speed_text = self.small_font.render(f"速度: {speed_value:.1f}", True, TEXT_COLOR)
        self.screen.blit(speed_text, (slider_x + slider_width + 20, slider_y - 5))
        
        # 绘制按钮
        for button in self.settings_buttons:
            button.draw(self.screen)
            
        pygame.display.flip()
    
    def save_game(self):
        # 创建存档数据
        save_data = {
            'grid': self.grid,
            'current_piece': self.current_piece,
            'next_piece': self.next_piece,
            'score': self.score,
            'level': self.level,
            'lines_cleared': self.lines_cleared,
            'game_mode': self.game_mode,
            'settings': self.settings  # 保存玩家设置
        }
        
        # 保存到文件
        try:
            with open(SAVE_FILE, 'w') as f:
                json.dump(save_data, f)
            return True
        except Exception as e:
            print(f"保存游戏失败: {e}")
            return False
    
    def load_high_score(self):
        # 检查最高分文件是否存在
        if not os.path.exists(HIGH_SCORE_FILE):
            return 0
            
        # 读取最高分
        try:
            with open(HIGH_SCORE_FILE, 'r') as f:
                high_score_data = json.load(f)
                return high_score_data.get('high_score', 0)
        except Exception as e:
            print(f"加载最高分失败: {e}")
            return 0
    
    def save_high_score(self):
        # 保存最高分
        try:
            with open(HIGH_SCORE_FILE, 'w') as f:
                json.dump({'high_score': self.high_score}, f)
            return True
        except Exception as e:
            print(f"保存最高分失败: {e}")
            return False
    
    def load_game(self):
        # 检查存档文件是否存在
        if not os.path.exists(SAVE_FILE):
            return False
            
        # 读取存档
        try:
            with open(SAVE_FILE, 'r') as f:
                save_data = json.load(f)
                
            # 恢复游戏状态
            self.grid = save_data['grid']
            self.current_piece = save_data['current_piece']
            self.next_piece = save_data['next_piece']
            self.score = save_data['score']
            self.level = save_data['level']
            self.lines_cleared = save_data['lines_cleared']
            self.game_mode = save_data['game_mode']
            
            # 加载设置（如果存在）
            if 'settings' in save_data:
                self.settings = save_data['settings']
                # 更新底部投影按钮文本
                text = "底部投影: 开" if self.settings['show_ghost_piece'] else "底部投影: 关"
                self.settings_buttons[8].text = text
                # 更新速度滑块位置
                self.speed_slider_pos = (2.0 - self.settings['base_speed']) / 1.9
            
            self.game_over = False
            self.last_drop_time = time.time()
            
            # 根据游戏模式和设置调整速度
            if self.game_mode == MODE_SPEED:
                self.drop_speed = self.settings['base_speed'] * 0.5
            else:
                self.drop_speed = max(0.05, self.settings['base_speed'] - (self.level - 1) * SPEED_INCREASE)
                
            return True
        except Exception as e:
            print(f"加载游戏失败: {e}")
            return False
    
    def handle_events(self):
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False
                
            # 获取鼠标位置
            mouse_pos = pygame.mouse.get_pos()
            
            # 主菜单状态
            if self.state == STATE_MENU:
                # 更新按钮悬停状态
                for button in self.menu_buttons:
                    button.check_hover(mouse_pos)
                    
                # 处理按钮点击
                if event.type == pygame.MOUSEBUTTONDOWN:
                    if self.menu_buttons[0].is_clicked(mouse_pos, event):  # 开始游戏
                        self.reset_game()
                        self.state = STATE_PLAYING
                    elif self.menu_buttons[1].is_clicked(mouse_pos, event):  # 继续游戏
                        if self.load_game():
                            self.state = STATE_PLAYING
                    elif self.menu_buttons[2].is_clicked(mouse_pos, event):  # 游戏模式
                        self.state = STATE_MODE_SELECT
                    elif self.menu_buttons[3].is_clicked(mouse_pos, event):  # 设置
                        self.state = STATE_SETTINGS
            
            # 模式选择状态
            elif self.state == STATE_MODE_SELECT:
                # 更新按钮悬停状态
                for button in self.mode_buttons:
                    button.check_hover(mouse_pos)
                    
                # 处理按钮点击
                if event.type == pygame.MOUSEBUTTONDOWN:
                    if self.mode_buttons[0].is_clicked(mouse_pos, event):  # 经典模式
                        self.game_mode = MODE_CLASSIC
                        self.state = STATE_MENU
                    elif self.mode_buttons[1].is_clicked(mouse_pos, event):  # 极速模式
                        self.game_mode = MODE_SPEED
                        self.state = STATE_MENU
                    elif self.mode_buttons[2].is_clicked(mouse_pos, event):  # 解谜模式
                        self.game_mode = MODE_PUZZLE
                        self.state = STATE_MENU
                    elif self.mode_buttons[3].is_clicked(mouse_pos, event):  # 返回
                        self.state = STATE_MENU
                        
            # 设置界面状态
            elif self.state == STATE_SETTINGS:
                # 更新按钮悬停状态
                for button in self.settings_buttons:
                    button.check_hover(mouse_pos)
                    
                # 处理滑块拖动
                if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
                    # 检查是否点击了滑块区域
                    slider_width = 200
                    slider_x = SCREEN_WIDTH // 2 - slider_width // 2
                    slider_y = SCREEN_HEIGHT // 2 + 130  # 更新滑块位置
                    slider_rect = pygame.Rect(slider_x, slider_y - 10, slider_width, 20)
                    
                    if slider_rect.collidepoint(mouse_pos):
                        # 计算滑块位置
                        self.speed_slider_pos = (mouse_pos[0] - slider_x) / slider_width
                        self.speed_slider_pos = max(0, min(1, self.speed_slider_pos))
                        # 更新速度设置 (0.1-2.0秒/格)
                        self.settings['base_speed'] = 2.0 - self.speed_slider_pos * 1.9
                
                # 处理鼠标拖动滑块
                if event.type == pygame.MOUSEMOTION and pygame.mouse.get_pressed()[0]:
                    slider_width = 200
                    slider_x = SCREEN_WIDTH // 2 - slider_width // 2
                    slider_y = SCREEN_HEIGHT // 2 + 130  # 更新滑块位置
                    slider_rect = pygame.Rect(slider_x, slider_y - 10, slider_width, 20)
                    
                    if slider_rect.collidepoint(mouse_pos):
                        # 计算滑块位置
                        self.speed_slider_pos = (mouse_pos[0] - slider_x) / slider_width
                        self.speed_slider_pos = max(0, min(1, self.speed_slider_pos))
                        # 更新速度设置 (0.1-2.0秒/格)
                        self.settings['base_speed'] = 2.0 - self.speed_slider_pos * 1.9
                
                # 处理按钮点击
                if event.type == pygame.MOUSEBUTTONDOWN:
                    # 颜色选择器左右按钮
                    if self.settings_buttons[0].is_clicked(mouse_pos, event):  # 左按钮
                        self.selected_color_index = (self.selected_color_index - 1) % len(self.color_names)
                    elif self.settings_buttons[1].is_clicked(mouse_pos, event):  # 右按钮
                        self.selected_color_index = (self.selected_color_index + 1) % len(self.color_names)
                    
                    # 颜色调整按钮
                    elif self.settings_buttons[2].is_clicked(mouse_pos, event):  # R+
                        r, g, b = self.settings['colors'][self.selected_color_index]
                        self.settings['colors'][self.selected_color_index] = (min(255, r + 10), g, b)
                    elif self.settings_buttons[3].is_clicked(mouse_pos, event):  # R-
                        r, g, b = self.settings['colors'][self.selected_color_index]
                        self.settings['colors'][self.selected_color_index] = (max(0, r - 10), g, b)
                    elif self.settings_buttons[4].is_clicked(mouse_pos, event):  # G+
                        r, g, b = self.settings['colors'][self.selected_color_index]
                        self.settings['colors'][self.selected_color_index] = (r, min(255, g + 10), b)
                    elif self.settings_buttons[5].is_clicked(mouse_pos, event):  # G-
                        r, g, b = self.settings['colors'][self.selected_color_index]
                        self.settings['colors'][self.selected_color_index] = (r, max(0, g - 10), b)
                    elif self.settings_buttons[6].is_clicked(mouse_pos, event):  # B+
                        r, g, b = self.settings['colors'][self.selected_color_index]
                        self.settings['colors'][self.selected_color_index] = (r, g, min(255, b + 10))
                    elif self.settings_buttons[7].is_clicked(mouse_pos, event):  # B-
                        r, g, b = self.settings['colors'][self.selected_color_index]
                        self.settings['colors'][self.selected_color_index] = (r, g, max(0, b - 10))
                    
                    # 底部投影开关
                    elif self.settings_buttons[8].is_clicked(mouse_pos, event):
                        self.settings['show_ghost_piece'] = not self.settings['show_ghost_piece']
                        # 更新按钮文本
                        text = "底部投影: 开" if self.settings['show_ghost_piece'] else "底部投影: 关"
                        self.settings_buttons[8].text = text
                    
                    # 返回按钮
                    elif self.settings_buttons[9].is_clicked(mouse_pos, event):
                        self.state = STATE_MENU
            
            # 游戏暂停状态
            elif self.state == STATE_PAUSED:
                # 更新按钮悬停状态
                for button in self.pause_buttons:
                    button.check_hover(mouse_pos)
                    
                # 处理按钮点击
                if event.type == pygame.MOUSEBUTTONDOWN:
                    if self.pause_buttons[0].is_clicked(mouse_pos, event):  # 继续游戏
                        self.state = STATE_PLAYING
                    elif self.pause_buttons[1].is_clicked(mouse_pos, event):  # 保存游戏
                        self.save_game()
                    elif self.pause_buttons[2].is_clicked(mouse_pos, event):  # 返回主菜单
                        self.save_game()  # 自动保存游戏状态
                        self.state = STATE_MENU
                        
            # 游戏进行状态
            elif self.state == STATE_PLAYING:
                if self.game_over:
                    # 游戏结束时，处理再来一局按钮
                    # 更新按钮悬停状态
                    for button in self.game_over_buttons:
                        button.check_hover(mouse_pos)
                    
                    # 处理按钮点击
                    if event.type == pygame.MOUSEBUTTONDOWN:
                        if self.game_over_buttons[0].is_clicked(mouse_pos, event):  # 再来一局
                            self.reset_game()
                else:
                    # 游戏进行中的按键处理
                    if event.type == pygame.KEYDOWN:
                        if event.key == pygame.K_UP:
                            self.current_piece = self.rotate_piece(self.current_piece)
                        elif event.key == pygame.K_SPACE:
                            # 硬降（直接落到底部）
                            while self.valid_position(self.current_piece, y_offset=1):
                                self.current_piece['y'] += 1
                            self.merge_piece()
                            self.clear_lines()
                            self.current_piece = self.next_piece
                            self.next_piece = self.new_piece()
                            if not self.valid_position(self.current_piece):
                                self.game_over = True
                            self.last_drop_time = time.time()
                        elif event.key == pygame.K_p or event.key == pygame.K_ESCAPE:
                            self.state = STATE_PAUSED
                    
        return True
        
    def update(self):
        # 只有在游戏进行状态下才更新游戏逻辑
        if self.state != STATE_PLAYING or self.game_over:
            return
        
        # 处理长按键
        current_time = pygame.time.get_ticks()
        keys = pygame.key.get_pressed()
        
        # 左移
        if keys[pygame.K_LEFT]:
            if self.key_press_start['left'] == 0:
                self.key_press_start['left'] = current_time
            
            # 计算按键持续时间
            key_duration = current_time - self.key_press_start['left']
            
            # 判断是否应该响应按键
            if key_duration < self.fast_repeat_threshold:
                # 未达到快速重复阈值，使用正常延迟
                if current_time - self.last_key_time['left'] > self.key_repeat_delay:
                    if self.valid_position(self.current_piece, x_offset=-1):
                        self.current_piece['x'] -= 1
                    self.last_key_time['left'] = current_time
            else:
                # 达到快速重复阈值，使用更短的间隔
                if current_time - self.last_key_time['left'] > self.key_repeat_interval:
                    if self.valid_position(self.current_piece, x_offset=-1):
                        self.current_piece['x'] -= 1
                    self.last_key_time['left'] = current_time
        else:
            self.last_key_time['left'] = 0
            self.key_press_start['left'] = 0
        
        # 右移
        if keys[pygame.K_RIGHT]:
            if self.key_press_start['right'] == 0:
                self.key_press_start['right'] = current_time
            
            # 计算按键持续时间
            key_duration = current_time - self.key_press_start['right']
            
            # 判断是否应该响应按键
            if key_duration < self.fast_repeat_threshold:
                # 未达到快速重复阈值，使用正常延迟
                if current_time - self.last_key_time['right'] > self.key_repeat_delay:
                    if self.valid_position(self.current_piece, x_offset=1):
                        self.current_piece['x'] += 1
                    self.last_key_time['right'] = current_time
            else:
                # 达到快速重复阈值，使用更短的间隔
                if current_time - self.last_key_time['right'] > self.key_repeat_interval:
                    if self.valid_position(self.current_piece, x_offset=1):
                        self.current_piece['x'] += 1
                    self.last_key_time['right'] = current_time
        else:
            self.last_key_time['right'] = 0
            self.key_press_start['right'] = 0
        
        # 下移
        if keys[pygame.K_DOWN]:
            if self.key_press_start['down'] == 0:
                self.key_press_start['down'] = current_time
            
            # 计算按键持续时间
            key_duration = current_time - self.key_press_start['down']
            
            # 判断是否应该响应按键
            if key_duration < self.fast_repeat_threshold:
                # 未达到快速重复阈值，使用正常延迟
                if current_time - self.last_key_time['down'] > self.key_repeat_delay:
                    if self.valid_position(self.current_piece, y_offset=1):
                        self.current_piece['y'] += 1
                        self.last_drop_time = time.time()  # 重置下落时间
                    self.last_key_time['down'] = current_time
            else:
                # 达到快速重复阈值，使用更短的间隔
                if current_time - self.last_key_time['down'] > self.key_repeat_interval:
                    if self.valid_position(self.current_piece, y_offset=1):
                        self.current_piece['y'] += 1
                        self.last_drop_time = time.time()  # 重置下落时间
                    self.last_key_time['down'] = current_time
        else:
            self.last_key_time['down'] = 0
            self.key_press_start['down'] = 0
            
        # 自动下落
        current_time_seconds = time.time()
        if current_time_seconds - self.last_drop_time > self.drop_speed:
            if self.valid_position(self.current_piece, y_offset=1):
                self.current_piece['y'] += 1
            else:
                self.merge_piece()
                self.clear_lines()
                self.current_piece = self.next_piece
                self.next_piece = self.new_piece()
                if not self.valid_position(self.current_piece):
                    self.game_over = True
                    # 检查是否达到最高分
                    if self.score > self.high_score:
                        self.high_score = self.score
                        self.save_high_score()
            self.last_drop_time = current_time_seconds
            
    def draw(self):
        # 根据当前游戏状态调用相应的绘制方法
        if self.state == STATE_MENU:
            self.draw_menu()
        elif self.state == STATE_MODE_SELECT:
            self.draw_mode_select()
        elif self.state == STATE_SETTINGS:
            self.draw_settings()
        elif self.state == STATE_PLAYING:
            # 游戏中状态的绘制
            self.screen.fill(BACKGROUND_COLOR)
            
            # 绘制游戏元素
            self.draw_grid()
            
            if self.settings['show_ghost_piece']:
                self.draw_ghost_piece()
                
            self.draw_current_piece()
            self.draw_next_piece()
            self.draw_info()
            
            if self.game_over:
                self.draw_game_over()
                
            pygame.display.flip()
        elif self.state == STATE_PAUSED:
            # 暂停状态的绘制
            self.screen.fill(BACKGROUND_COLOR)
            
            # 绘制游戏元素
            self.draw_grid()
            
            if self.settings['show_ghost_piece']:
                self.draw_ghost_piece()
                
            self.draw_current_piece()
            self.draw_next_piece()
            self.draw_info()
            self.draw_pause()
            
            pygame.display.flip()
            
    def run(self):
        running = True
        while running:
            running = self.handle_events()
            self.update()
            self.draw()
            self.clock.tick(60)
        pygame.quit()

if __name__ == "__main__":
    game = Tetris()
    game.run()